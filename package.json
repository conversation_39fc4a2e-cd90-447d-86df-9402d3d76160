{"name": "axero-documentation-migrator", "version": "1.0.0", "description": "Content migration script for Axero documentation website", "main": "migration-script-fixed.js", "scripts": {"migrate": "node migration-script-fixed.js", "install-deps": "npm install"}, "keywords": ["<PERSON>ro", "documentation", "migration", "web-scraping", "content-management"], "author": "Axero Documentation Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "dotenv": "^16.6.1", "turndown": "^7.1.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/axero/documentation-migrator"}, "bugs": {"url": "https://github.com/axero/documentation-migrator/issues"}, "homepage": "https://github.com/axero/documentation-migrator#readme"}