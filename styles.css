/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #0066cc;
  --primary-hover: #0052a3;
  --primary-light: #3385d6;
  --secondary-color: #4a5568;
  --background-color: #ffffff;
  --surface-color: #f7fafc;
  --surface-hover: #edf2f7;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --sidebar-width: 280px;
  --outline-width: 240px;
  --header-height: 60px;
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --success-color: #38a169;
  --success-light: #48bb78;
  --warning-color: #d69e2e;
  --warning-light: #ed8936;
  --error-color: #e53e3e;
  --error-light: #f56565;
  --info-color: #0066cc;
  --info-light: #3385d6;
}

/* Dark theme variables */
[data-theme='dark'] {
  --primary-color: #4299e1;
  --primary-hover: #3182ce;
  --primary-light: #63b3ed;
  --secondary-color: #a0aec0;
  --background-color: #1a202c;
  --surface-color: #2d3748;
  --surface-hover: #4a5568;
  --border-color: #4a5568;
  --border-light: #718096;
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #a0aec0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background-color);
}

/* Layout Container */
.layout-container {
  display: flex;
  min-height: 100vh;
  position: relative;
  padding-top: var(--header-height);
}

/* Header Styles */
.main-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background-color: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  backdrop-filter: blur(8px);
}

.header-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 1rem;
  max-width: 100%;
  gap: 2rem;
}

.header-search {
  flex: 1;
  max-width: 400px;
  position: relative;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 0.5rem 2.5rem 0.5rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background-color: var(--surface-color);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--text-muted);
  pointer-events: none;
}

.search-clear {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.2s ease;
}

.search-clear:hover {
  color: var(--text-primary);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1001;
  display: none;
}

.search-result {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-result:hover {
  background-color: var(--surface-color);
}

.search-result:last-child {
  border-bottom: none;
}

.search-result-title {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.search-result-excerpt {
  font-size: 0.8125rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.search-highlight {
  background-color: rgba(37, 99, 235, 0.2);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-left: auto;
}

.theme-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.theme-toggle:hover {
  background-color: var(--surface-color);
  color: var(--primary-color);
}

.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

/* Sidebar Styles */
.sidebar {
  width: var(--sidebar-width);
  background-color: var(--surface-color);
  border-right: 1px solid var(--border-color);
  position: fixed;
  top: var(--header-height);
  left: 0;
  height: calc(100vh - var(--header-height));
  overflow-y: auto;
  z-index: 100;
  transition: transform 0.3s ease;
}

.sidebar-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
}

.logo {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
}

.sidebar-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.sidebar-toggle span {
  width: 20px;
  height: 2px;
  background-color: var(--text-primary);
  margin: 2px 0;
  transition: 0.3s;
}

.sidebar-content {
  padding: 1rem 0;
}

/* Navigation Tree */
.nav-tree {
  list-style: none;
}

.nav-item {
  margin-bottom: 0.125rem;
}

.nav-link,
.nav-toggle {
  display: flex;
  align-items: center;
  padding: 0.5rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  line-height: 1.4;
}

.nav-link:hover,
.nav-toggle:hover {
  background-color: var(--surface-hover);
  color: var(--primary-color);
}

.nav-link.active {
  background-color: var(--surface-hover);
  color: var(--primary-color);
  border-right: 3px solid var(--primary-color);
  font-weight: 600;
}

.nav-icon {
  margin-right: 0.75rem;
  font-size: 0.875rem;
  width: 1rem;
  text-align: center;
  color: var(--text-muted);
}

.toggle-icon {
  margin-left: auto;
  font-size: 0.75rem;
  transition: transform 0.2s ease;
}

.nav-toggle.expanded .toggle-icon {
  transform: rotate(90deg);
}

.nav-toggle.expanded .toggle-icon i {
  transform: rotate(90deg);
}

.nav-children {
  list-style: none;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.45s ease, padding 0.45s ease, opacity 0.3s ease;
  background-color: rgba(248, 250, 252, 0.5);
  border-left: 2px solid var(--border-color);
  margin-left: 1rem;
  opacity: 0;
}

/* Force top-level nav items to have no indentation */
.nav-tree > .nav-item {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.nav-tree > .nav-item > .nav-link,
.nav-tree > .nav-item > .nav-toggle {
  margin-left: 0 !important;
  padding-left: 1.5rem !important;
}

/* Ensure all direct children of nav-tree are at the same level */
.nav-tree > li {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.nav-tree > li > a,
.nav-tree > li > button {
  margin-left: 0 !important;
  padding-left: 1.5rem !important;
}

/* Override any inherited indentation */
.nav-tree .nav-item:not(.nav-children .nav-item) {
  margin-left: 0 !important;
}

.nav-tree .nav-link:not(.nav-children .nav-link),
.nav-tree .nav-toggle:not(.nav-children .nav-toggle) {
  padding-left: 1.5rem !important;
}

/* Force all top-level navigation items to be at the same level */
.nav-tree > li.nav-item {
  margin-left: 0 !important;
  padding-left: 0 !important;
  position: relative;
}

.nav-tree > li.nav-item > a.nav-link,
.nav-tree > li.nav-item > button.nav-toggle {
  margin-left: 0 !important;
  padding-left: 1.5rem !important;
  display: flex;
  align-items: center;
}

/* Ensure no inherited indentation affects top-level items */
.nav-tree > li.nav-item * {
  margin-left: 0;
}

.nav-tree > li.nav-item > a.nav-link > *,
.nav-tree > li.nav-item > button.nav-toggle > * {
  margin-left: 0;
}

.nav-children.expanded {
  max-height: 500px;
  padding: 0.25rem 0;
  opacity: 1;
}

.nav-children .nav-link {
  padding: 0.375rem 1.5rem;
  font-size: 0.85rem;
  font-weight: 400;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  border-radius: 4px;
  margin: 0.125rem 0.5rem;
}

.nav-children .nav-link:hover {
  background-color: rgba(0, 102, 204, 0.08);
  color: var(--primary-color);
}

.nav-children .nav-link.active {
  background-color: rgba(0, 102, 204, 0.12);
  color: var(--primary-color);
  font-weight: 500;
}

/* Navigation Subsections */
.nav-subsection {
  margin: 0.5rem 0;
}

.nav-subsection-title {
  display: block;
  padding: 0.75rem 1rem 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-color);
  text-transform: none;
  letter-spacing: 0.25px;
  border-bottom: 1px solid rgba(0, 102, 204, 0.15);
  margin-bottom: 0.5rem;
  position: relative;
}

.nav-subsection-items {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-subsection-items .nav-link {
  padding: 0.375rem 1.75rem;
  font-size: 0.85rem;
  font-weight: 400;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  border-radius: 0.25rem;
  margin: 0.125rem 0.75rem;
}

.nav-subsection-items .nav-link:hover {
  background-color: rgba(0, 102, 204, 0.08);
  color: var(--primary-color);
}

.nav-subsection-items .nav-link.active {
  background-color: rgba(0, 102, 204, 0.12);
  color: var(--primary-color);
  font-weight: 500;
}

/* Enhanced nav-toggle for categories with proper layout */
.has-children > .nav-toggle {
  width: 100%;
  justify-content: space-between;
  text-align: left;
  white-space: nowrap;
}

.has-children > .nav-toggle .nav-title {
  flex: 1;
  text-align: left;
  margin-left: 0.75rem;
  margin-right: 0.75rem;
  overflow: hidden;
  text-overflow: ellipsis;
}

.has-children > .nav-toggle.active {
  background-color: var(--surface-hover);
  color: var(--primary-color);
  border-right: 3px solid var(--primary-color);
}

.has-children > .nav-toggle:hover .nav-icon,
.has-children > .nav-toggle.active .nav-icon {
  color: var(--primary-color);
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  margin-right: var(--outline-width);
  min-height: 100vh;
}

.content-wrapper {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

/* Breadcrumb Navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  font-size: 0.875rem;
  color: var(--text-muted);
}

.breadcrumb-link {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-link:hover {
  color: var(--text-primary);
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  color: var(--text-muted);
}

.breadcrumb-category {
  color: var(--text-secondary);
  font-weight: 400;
}

.breadcrumb-category-bold {
  color: var(--text-primary);
  font-weight: 600;
}

.breadcrumb-current {
  color: var(--text-primary);
  font-weight: 500;
}

.content {
  line-height: 1.7;
}

/* Typography */
.content h1 {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
  line-height: 1.2;
}

.content h2 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 2rem 0 1rem 0;
  color: var(--text-primary);
  line-height: 1.3;
}

.content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
  color: var(--text-primary);
  line-height: 1.4;
}

.content p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

.content ul,
.content ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.content li {
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
}

.content code {
  background-color: var(--surface-color);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.content pre {
  background-color: #2d3748;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.content pre code {
  background: none;
  padding: 0;
  color: #e2e8f0;
}

/* Code block enhancements */
.code-block-wrapper {
  position: relative;
  margin: 1rem 0;
}

.code-block-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #374151;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem 0.5rem 0 0;
  font-size: 0.8125rem;
  color: #d1d5db;
}

.code-language {
  font-weight: 500;
}

.copy-code-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.copy-code-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #f3f4f6;
}

.copy-code-btn.copied {
  color: var(--success-color);
}

/* Callout boxes */
.callout {
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  border-left: 4px solid;
  position: relative;
}

.callout-info {
  background-color: rgba(59, 130, 246, 0.1);
  border-left-color: var(--info-color);
  color: var(--text-primary);
}

.callout-warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-left-color: var(--warning-color);
  color: var(--text-primary);
}

.callout-error {
  background-color: rgba(239, 68, 68, 0.1);
  border-left-color: var(--error-color);
  color: var(--text-primary);
}

.callout-success {
  background-color: rgba(16, 185, 129, 0.1);
  border-left-color: var(--success-color);
  color: var(--text-primary);
}

.callout-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Tabbed content */
.tabs {
  margin: 1rem 0;
}

.tab-list {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  color: var(--text-secondary);
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-button:hover {
  color: var(--text-primary);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Page Navigation */
.page-navigation {
  display: flex;
  justify-content: space-between;
  margin: 3rem 0 2rem 0;
  padding: 2rem 0;
  border-top: 1px solid var(--border-color);
  gap: 1rem;
}

.page-nav-item {
  flex: 1;
  max-width: 48%;
  display: flex;
}

.page-nav-item.next {
  justify-content: flex-end;
}

.page-nav-link {
  display: flex;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  text-decoration: none;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  gap: 1rem;
  background-color: var(--background-color);
  min-height: 80px;
  width: 100%;
}

.page-nav-link:hover {
  border-color: var(--primary-color);
  background-color: var(--surface-color);
}

.page-nav-link:hover .page-nav-title {
  color: var(--primary-color);
}

.page-nav-link i {
  font-size: 1.25rem;
  color: var(--primary-color);
}

.page-nav-content {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  z-index: 1;
  position: relative;
}

.page-nav-label {
  font-size: 0.75rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 600;
}

.page-nav-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
  line-height: 1.4;
  transition: color 0.3s ease;
}

/* Feedback Section */
.feedback-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: var(--surface-color);
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
}

.feedback-section h4 {
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.feedback-buttons {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.feedback-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background-color: var(--background-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.feedback-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.feedback-btn.selected {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.feedback-form {
  margin-top: 1rem;
}

.feedback-form textarea {
  width: 100%;
  min-height: 100px;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background-color: var(--background-color);
  color: var(--text-primary);
  font-family: inherit;
  resize: vertical;
}

.feedback-form-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--background-color);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

.btn-secondary:hover {
  background-color: var(--surface-color);
}

/* Page Outline */
.page-outline {
  width: var(--outline-width);
  background-color: var(--background-color);
  border-left: 1px solid var(--border-color);
  position: fixed;
  top: var(--header-height);
  right: 0;
  height: calc(100vh - var(--header-height));
  overflow-y: auto;
  z-index: 50;
  padding: 2rem 1.5rem;
}

.outline-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.outline-header h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

.outline-toggle {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.outline-toggle:hover {
  color: var(--text-primary);
  background-color: var(--surface-color);
}

.outline-nav {
  list-style: none;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  max-height: 1000px;
  opacity: 1;
  overflow: hidden;
}

.outline-nav.collapsed {
  max-height: 0;
  opacity: 0;
}

.outline-nav a {
  display: block;
  padding: 0.375rem 0;
  color: var(--text-muted);
  text-decoration: none;
  font-size: 0.85rem;
  line-height: 1.4;
  transition: color 0.2s ease;
  border-left: 2px solid transparent;
  padding-left: 0.75rem;
}

.outline-nav a:hover {
  color: var(--primary-color);
}

.outline-nav a.active {
  color: var(--primary-color);
  border-left-color: var(--primary-color);
}

.outline-nav .outline-h3 {
  padding-left: 1.5rem;
  font-size: 0.75rem;
}

/* Reading Progress */
.reading-progress {
  margin: 2rem 0;
  padding: 1rem 0;
  border-top: 1px solid var(--border-color);
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
  width: 0%;
}

.progress-text {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* Recently Viewed */
.recently-viewed {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.recently-viewed h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.recent-list {
  list-style: none;
}

.recent-list li {
  margin-bottom: 0.5rem;
}

.recent-list a {
  display: block;
  padding: 0.375rem 0;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.8125rem;
  line-height: 1.4;
  transition: color 0.2s ease;
}

.recent-list a:hover {
  color: var(--primary-color);
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3rem;
  height: 3rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  z-index: 1000;
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: #1d4ed8;
  transform: translateY(-2px);
}

/* Keyboard Shortcuts Help */
.shortcuts-help {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.shortcuts-help.visible {
  opacity: 1;
  visibility: visible;
}

.shortcuts-content {
  background-color: var(--background-color);
  border-radius: 0.5rem;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  position: relative;
  box-shadow: var(--shadow-lg);
}

.shortcuts-content h3 {
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.shortcut-item kbd {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.8125rem;
  font-family: monospace;
  color: var(--text-primary);
}

.shortcut-item span {
  color: var(--text-secondary);
}

.close-shortcuts {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.close-shortcuts:hover {
  color: var(--text-primary);
  background-color: var(--surface-color);
}

/* Mobile Styles */
@media (max-width: 1024px) {
  .page-outline {
    display: none;
  }

  .main-content {
    margin-right: 0;
  }

  .header-search {
    margin: 0 1rem;
  }
}

/* Documentation Overview Styles */
.doc-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.overview-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.overview-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 102, 204, 0.1);
  border-color: var(--primary-color);
}

.overview-section h3 {
  color: var(--primary-color);
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.overview-section h3 i {
  font-size: 1.2rem;
}

.overview-section p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    top: 0;
    height: 100vh;
    z-index: 1001;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .mobile-only {
    display: block;
  }

  .desktop-only {
    display: none;
  }

  .main-content {
    margin-left: 0;
  }

  .content-wrapper {
    padding: 1rem;
  }

  .header-search {
    max-width: none;
    flex: 1;
  }

  .header-controls {
    gap: 0.5rem;
  }

  .page-navigation {
    flex-direction: column;
    gap: 1rem;
  }

  .page-nav-item {
    max-width: 100%;
  }

  .page-nav-item.next {
    text-align: left;
  }

  .feedback-buttons {
    flex-direction: column;
  }

  .back-to-top {
    bottom: 1rem;
    right: 1rem;
    width: 2.5rem;
    height: 2.5rem;
  }

  .mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .mobile-overlay.active {
    opacity: 1;
    visibility: visible;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Loading animation */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--text-muted);
}
