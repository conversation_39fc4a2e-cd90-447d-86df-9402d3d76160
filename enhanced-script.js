// Enhanced Documentation Website JavaScript
class EnhancedDocumentationSite {
  constructor() {
    this.currentPage = 'introduction';
    this.pages = {};
    this.searchIndex = [];
    this.recentPages = JSON.parse(localStorage.getItem('recentPages') || '[]');
    this.theme = localStorage.getItem('theme') || 'light';
    this.pageOrder = ['introduction', 'installation', 'quick-start', 'configuration', 'authentication', 'endpoints', 'examples', 'tutorial-basics', 'tutorial-advanced', 'tutorial-integration', 'customization', 'troubleshooting', 'performance', 'faq', 'changelog', 'support'];
    this.init();
  }

  init() {
    this.applyTheme();
    this.setupEventListeners();
    this.setupSearch();
    this.setupKeyboardShortcuts();
    this.handleInitialRoute();
    this.loadPage('introduction');
    this.setupMobileMenu();
    this.setupScrollSpy();
    this.setupOutlineToggle();
    this.updateRecentPages();

    // Initialize navigation immediately
    this.setupCorrectNavigation();
  }

  setupCorrectNavigation() {
    const navTree = document.querySelector('.nav-tree');
    if (!navTree) return;

    // Clear existing content
    navTree.innerHTML = '';

    // Create the correct navigation structure
    navTree.innerHTML = `
      <li class="nav-item">
        <a href="#introduction" class="nav-link active" data-page="introduction">
          <i class="fas fa-home nav-icon"></i>
          Introduction
        </a>
      </li>
      <li class="nav-item has-children">
        <button class="nav-toggle" data-page="enabling-api-access">
          <i class="fas fa-cog nav-icon"></i>
          <span class="nav-title">Setup & Configuration</span>
          <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
        </button>
        <ul class="nav-children">
          <li class="nav-subsection">
            <span class="nav-subsection-title">API Setup</span>
            <ul class="nav-subsection-items">
              <li><a href="#enabling-api-access" class="nav-link" data-page="enabling-api-access">Enabling API Access</a></li>
              <li><a href="#creating-bearer-tokens" class="nav-link" data-page="creating-bearer-tokens">Creating Bearer Tokens</a></li>
            </ul>
          </li>
        </ul>
      </li>
      <li class="nav-item has-children">
        <button class="nav-toggle" data-page="saml-2-0-sso">
          <i class="fas fa-shield-alt nav-icon"></i>
          <span class="nav-title">Authentication & Security</span>
          <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
        </button>
        <ul class="nav-children">
          <li class="nav-subsection">
            <span class="nav-subsection-title">SSO Authentication</span>
            <ul class="nav-subsection-items">
              <li><a href="#saml-2-0-sso" class="nav-link" data-page="saml-2-0-sso">SAML 2.0 SSO</a></li>
            </ul>
          </li>
        </ul>
      </li>
      <li class="nav-item has-children">
        <button class="nav-toggle" data-page="journeys">
          <i class="fas fa-graduation-cap nav-icon"></i>
          <span class="nav-title">Tutorials & Guides</span>
          <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
        </button>
        <ul class="nav-children">
          <li class="nav-subsection">
            <span class="nav-subsection-title">Journey Management</span>
            <ul class="nav-subsection-items">
              <li><a href="#journeys" class="nav-link" data-page="journeys">Journeys</a></li>
            </ul>
          </li>
        </ul>
      </li>
      <li class="nav-item has-children">
        <button class="nav-toggle" data-page="using-the-rest-api">
          <i class="fas fa-code nav-icon"></i>
          <span class="nav-title">API Reference</span>
          <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
        </button>
        <ul class="nav-children">
          <li class="nav-subsection">
            <span class="nav-subsection-title">Getting Started</span>
            <ul class="nav-subsection-items">
              <li><a href="#using-the-rest-api" class="nav-link" data-page="using-the-rest-api">Using the REST API</a></li>
            </ul>
          </li>
        </ul>
      </li>
      <li class="nav-item has-children">
        <button class="nav-toggle" data-page="troubleshooting-guide-for-blurry-images">
          <i class="fas fa-wrench nav-icon"></i>
          <span class="nav-title">Troubleshooting</span>
          <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
        </button>
        <ul class="nav-children">
          <li class="nav-subsection">
            <span class="nav-subsection-title">Common Issues</span>
            <ul class="nav-subsection-items">
              <li><a href="#troubleshooting-guide-for-blurry-images" class="nav-link" data-page="troubleshooting-guide-for-blurry-images">Troubleshooting Guide for Blurry Images</a></li>
            </ul>
          </li>
        </ul>
      </li>
      <li class="nav-item has-children">
        <button class="nav-toggle" data-page="2022-enhancements">
          <i class="fas fa-history nav-icon"></i>
          <span class="nav-title">Changelog</span>
          <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
        </button>
        <ul class="nav-children">
          <li class="nav-subsection">
            <span class="nav-subsection-title">Version History</span>
            <ul class="nav-subsection-items">
              <li><a href="#2022-enhancements" class="nav-link" data-page="2022-enhancements">2022 Enhancements</a></li>
            </ul>
          </li>
        </ul>
      </li>
    `;

    // Attach event handlers immediately
    this.attachNavigationEvents();
  }

  applyTheme() {
    document.documentElement.setAttribute('data-theme', this.theme);
    const themeIcon = document.querySelector('#themeToggle i');
    if (themeIcon) {
      themeIcon.className = this.theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
  }

  setupEventListeners() {
    // Navigation links
    document.querySelectorAll('.nav-link').forEach((link) => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const page = link.dataset.page;
        if (page) {
          this.loadPage(page);
          this.setActiveNavLink(link);
          this.closeMobileMenu();
        }
      });
    });

    // Navigation toggles - removed duplicate handler, using the one below

    // Theme toggle
    const themeToggle = document.getElementById('themeToggle');
    themeToggle?.addEventListener('click', () => {
      this.toggleTheme();
    });

    // Back to top button
    const backToTop = document.getElementById('backToTop');
    backToTop?.addEventListener('click', () => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    // Feedback system
    this.setupFeedbackSystem();

    // Page navigation
    this.setupPageNavigation();

    // Scroll spy for back to top and progress
    window.addEventListener('scroll', () => {
      this.updateScrollElements();
    });
  }

  setupSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchClear = document.getElementById('searchClear');
    const searchResults = document.getElementById('searchResults');

    if (!searchInput) return;

    // Build search index
    this.buildSearchIndex();

    // Initialize Fuse.js for fuzzy search
    this.fuse = new Fuse(this.searchIndex, {
      keys: ['title', 'content', 'tags'],
      threshold: 0.3,
      includeMatches: true,
    });

    searchInput.addEventListener('input', (e) => {
      const query = e.target.value.trim();
      if (query.length > 2) {
        this.performSearch(query);
        searchClear.style.display = 'block';
      } else {
        this.hideSearchResults();
        searchClear.style.display = 'none';
      }
    });

    searchClear.addEventListener('click', () => {
      searchInput.value = '';
      this.hideSearchResults();
      searchClear.style.display = 'none';
      searchInput.focus();
    });

    // Hide search results when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.header-search')) {
        this.hideSearchResults();
      }
    });
  }

  buildSearchIndex() {
    // This would typically be built from your content
    this.searchIndex = [
      {
        id: 'introduction',
        title: 'Introduction',
        content: 'Welcome to Axero Documentation comprehensive guide platform features',
        tags: ['getting-started', 'overview'],
      },
      {
        id: 'installation',
        title: 'Installation',
        content: 'Install Axero npm system requirements Node.js',
        tags: ['setup', 'npm', 'nodejs'],
      },
      {
        id: 'quick-start',
        title: 'Quick Start',
        content: 'Create first project Axero tutorial guide',
        tags: ['tutorial', 'beginner', 'first-steps'],
      },
      {
        id: 'authentication',
        title: 'Authentication',
        content: 'API keys OAuth security authentication methods',
        tags: ['security', 'api', 'oauth'],
      },
      {
        id: 'endpoints',
        title: 'API Endpoints',
        content: 'REST API endpoints users data CRUD operations',
        tags: ['api', 'rest', 'endpoints'],
      },
      {
        id: 'examples',
        title: 'Examples',
        content: 'Code examples samples hello world components API integration forms testing',
        tags: ['examples', 'code', 'samples', 'tutorial'],
      },
    ];
  }

  performSearch(query) {
    const results = this.fuse.search(query);
    this.displaySearchResults(results, query);
  }

  displaySearchResults(results, query) {
    const searchResults = document.getElementById('searchResults');
    if (!searchResults) return;

    if (results.length === 0) {
      searchResults.innerHTML = '<div class="search-result">No results found</div>';
    } else {
      searchResults.innerHTML = results
        .slice(0, 5)
        .map((result) => {
          const item = result.item;
          const excerpt = this.highlightSearchTerms(item.content, query);
          return `
                    <div class="search-result" data-page="${item.id}">
                        <div class="search-result-title">${item.title}</div>
                        <div class="search-result-excerpt">${excerpt}</div>
                    </div>
                `;
        })
        .join('');

      // Add click handlers to search results
      searchResults.querySelectorAll('.search-result').forEach((result) => {
        result.addEventListener('click', () => {
          const page = result.dataset.page;
          if (page) {
            this.loadPage(page);
            this.hideSearchResults();
            document.getElementById('searchInput').value = '';
            document.getElementById('searchClear').style.display = 'none';
          }
        });
      });
    }

    searchResults.style.display = 'block';
  }

  highlightSearchTerms(text, query) {
    const words = query.toLowerCase().split(' ');
    let highlighted = text;
    words.forEach((word) => {
      const regex = new RegExp(`(${word})`, 'gi');
      highlighted = highlighted.replace(regex, '<span class="search-highlight">$1</span>');
    });
    return highlighted.substring(0, 150) + '...';
  }

  hideSearchResults() {
    const searchResults = document.getElementById('searchResults');
    if (searchResults) {
      searchResults.style.display = 'none';
    }
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Focus search with '/'
      if (e.key === '/' && !e.target.matches('input, textarea')) {
        e.preventDefault();
        document.getElementById('searchInput')?.focus();
      }

      // Close search/dialogs with Escape
      if (e.key === 'Escape') {
        this.hideSearchResults();
        document.getElementById('shortcutsHelp')?.classList.remove('visible');
        document.getElementById('searchInput')?.blur();
      }

      // Show shortcuts help with '?'
      if (e.key === '?' && !e.target.matches('input, textarea')) {
        e.preventDefault();
        document.getElementById('shortcutsHelp')?.classList.add('visible');
      }

      // Page navigation with arrow keys
      if (e.key === 'ArrowLeft' && !e.target.matches('input, textarea')) {
        this.navigateToPreviousPage();
      }
      if (e.key === 'ArrowRight' && !e.target.matches('input, textarea')) {
        this.navigateToNextPage();
      }
    });

    // Close shortcuts help
    document.getElementById('closeShortcuts')?.addEventListener('click', () => {
      document.getElementById('shortcutsHelp')?.classList.remove('visible');
    });
  }

  toggleTheme() {
    this.theme = this.theme === 'light' ? 'dark' : 'light';
    localStorage.setItem('theme', this.theme);
    this.applyTheme();
  }

  setupMobileMenu() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const mobileOverlay = document.getElementById('mobileOverlay');

    sidebarToggle?.addEventListener('click', () => {
      sidebar.classList.toggle('open');
      mobileOverlay.classList.toggle('active');
    });

    mobileOverlay?.addEventListener('click', () => {
      this.closeMobileMenu();
    });
  }

  closeMobileMenu() {
    const sidebar = document.getElementById('sidebar');
    const mobileOverlay = document.getElementById('mobileOverlay');
    sidebar?.classList.remove('open');
    mobileOverlay?.classList.remove('active');
  }

  setupScrollSpy() {
    window.addEventListener('scroll', () => {
      this.updateActiveOutlineLink();
      this.updateReadingProgress();
    });
  }

  updateScrollElements() {
    const backToTop = document.getElementById('backToTop');
    if (window.scrollY > 300) {
      backToTop?.classList.add('visible');
    } else {
      backToTop?.classList.remove('visible');
    }
  }

  setupOutlineToggle() {
    const outlineToggle = document.getElementById('outlineToggle');
    const outlineNav = document.getElementById('outlineNav');

    if (!outlineToggle || !outlineNav) return;

    outlineToggle.addEventListener('click', () => {
      const isCollapsed = outlineNav.classList.contains('collapsed');

      if (isCollapsed) {
        outlineNav.classList.remove('collapsed');
        outlineToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
        outlineToggle.title = 'Collapse outline';
      } else {
        outlineNav.classList.add('collapsed');
        outlineToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
        outlineToggle.title = 'Expand outline';
      }
    });
  }

  updateReadingProgress() {
    const content = document.getElementById('content');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    if (!content || !progressFill || !progressText) return;

    const contentHeight = content.scrollHeight;
    const windowHeight = window.innerHeight;
    const scrollTop = window.scrollY;
    const contentTop = content.offsetTop;

    const progress = Math.min(100, Math.max(0, ((scrollTop - contentTop + windowHeight) / contentHeight) * 100));

    progressFill.style.width = `${progress}%`;
    progressText.textContent = `${Math.round(progress)}% complete`;
  }



  setActiveNavLink(activeLink) {
    document.querySelectorAll('.nav-link').forEach((link) => {
      link.classList.remove('active');
    });
    activeLink.classList.add('active');
  }

  loadPage(pageName) {
    const content = document.getElementById('content');
    content.innerHTML = '<div class="loading">Loading...</div>';

    // Update breadcrumb
    this.updateBreadcrumb(pageName);

    // Add to recent pages
    this.addToRecentPages(pageName);

    // Simulate loading delay for demo
    setTimeout(() => {
      content.innerHTML = this.getPageContent(pageName);
      this.generatePageOutline();
      this.currentPage = pageName;
      this.updatePageNavigation();

      // Highlight code blocks
      if (window.Prism) {
        Prism.highlightAll();
      }

      // Add copy buttons to code blocks
      this.addCopyButtons();

      // Scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 300);
  }

  updateBreadcrumb(pageName) {
    const breadcrumb = document.getElementById('breadcrumb');
    if (!breadcrumb) return;

    const pageInfo = this.getPageInfo(pageName);
    const categoryTitle = pageInfo.categoryTitle;
    const pageTitle = pageInfo.title;

    if (pageName === 'introduction') {
      breadcrumb.innerHTML = `
                <a href="#introduction" class="breadcrumb-link">Home</a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">Introduction</span>
            `;
    } else if (categoryTitle && categoryTitle !== 'Introduction') {
      breadcrumb.innerHTML = `
                <a href="#introduction" class="breadcrumb-link">Home</a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-category-bold">${categoryTitle}</span>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">${pageTitle}</span>
            `;
    } else {
      breadcrumb.innerHTML = `
                <a href="#introduction" class="breadcrumb-link">Home</a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">${pageTitle}</span>
            `;
    }
  }

  findPageInNavigation(pageName) {
    const config = this.getNavigationConfig();

    for (const category of config.categories) {
      if (category.type === 'single' && category.pages.includes(pageName)) {
        return {
          categoryId: category.id,
          categoryTitle: category.title,
          subcategoryId: null,
          subcategoryTitle: null,
        };
      }

      if (category.subcategories) {
        for (const subcategory of category.subcategories) {
          if (subcategory.pages.includes(pageName)) {
            return {
              categoryId: category.id,
              categoryTitle: category.title,
              subcategoryId: subcategory.id,
              subcategoryTitle: subcategory.title,
            };
          }
        }
      }
    }

    return null;
  }

  getPageInfo(pageName) {
    // First check the detailed page index
    const pageIndex = this.getPageIndex();
    const navInfo = this.findPageInNavigation(pageName);

    if (pageIndex[pageName]) {
      const page = pageIndex[pageName];
      return {
        title: page.title,
        category: navInfo?.categoryId || page.category,
        categoryTitle: navInfo?.categoryTitle || this.getCategoryTitle(page.category),
        subcategory: navInfo?.subcategoryId,
        subcategoryTitle: navInfo?.subcategoryTitle,
      };
    }

    // Fallback to basic titles for simple pages
    const basicTitles = {
      introduction: 'Introduction',
      installation: 'Installation',
      'quick-start': 'Quick Start',
      configuration: 'Configuration',
      authentication: 'Authentication',
      endpoints: 'API Endpoints',
      examples: 'Examples',
      'tutorial-basics': 'Basic Tutorial',
      'tutorial-advanced': 'Advanced Tutorial',
      'tutorial-integration': 'Integration Guide',
      customization: 'Customization',
      troubleshooting: 'Troubleshooting',
      performance: 'Performance',
      faq: 'FAQ',
      changelog: 'Changelog',
      support: 'Support',
    };

    return {
      title: basicTitles[pageName] || 'Unknown Page',
      category: navInfo?.categoryId,
      categoryTitle: navInfo?.categoryTitle,
      subcategory: navInfo?.subcategoryId,
      subcategoryTitle: navInfo?.subcategoryTitle,
    };
  }

  getPageTitle(pageName) {
    return this.getPageInfo(pageName).title;
  }

  getCategoryTitle(category) {
    const categoryTitles = {
      'api-endpoints': 'API Reference',
      'examples-tutorials': 'Tutorials & Guides',
      authentication: 'Authentication & Security',
      configuration: 'Setup & Configuration',
      troubleshooting: 'Troubleshooting',
      changelog: 'Changelog',
      endpoints: 'API Reference',
      examples: 'Tutorials & Guides',
      setup: 'Setup & Configuration',
      security: 'Authentication & Security',
      tutorials: 'Tutorials & Guides',
      guides: 'Tutorials & Guides',
    };
    return categoryTitles[category] || category;
  }

  getNavigationConfig() {
    return {
      categories: [
        {
          id: 'introduction',
          title: 'Introduction',
          icon: 'fas fa-home',
          type: 'single',
          pages: ['introduction'],
        },
        {
          id: 'setup-configuration',
          title: 'Setup & Configuration',
          icon: 'fas fa-cog',
          type: 'category',
          subcategories: [
            {
              id: 'api-setup',
              title: 'API Setup',
              pages: ['enabling-api-access', 'creating-bearer-tokens'],
            },
            {
              id: 'email-settings',
              title: 'Email Settings',
              pages: ['how-to-enable-images-in-emails'],
            },
          ],
        },
        {
          id: 'authentication-security',
          title: 'Authentication & Security',
          icon: 'fas fa-shield-alt',
          type: 'category',
          subcategories: [
            {
              id: 'sso-authentication',
              title: 'SSO Authentication',
              pages: ['saml-2-0-sso', 'setup-guide-onelogin-saml-sso', 'transitioning-to-jwt-authentication'],
            },
          ],
        },
        {
          id: 'tutorials-guides',
          title: 'Tutorials & Guides',
          icon: 'fas fa-graduation-cap',
          type: 'category',
          subcategories: [
            {
              id: 'content-management',
              title: 'Content Management',
              pages: ['create-wiki-page', 'file-field'],
            },
            {
              id: 'journey-management',
              title: 'Journey Management',
              pages: ['journeys', 'create-journey', 'journey-lifecycle-management', 'managing-journey-participants', 'navigating-a-journey-as-a-user'],
            },
            {
              id: 'integration',
              title: 'Integration',
              pages: ['webhooks'],
            },
          ],
        },
        {
          id: 'api-reference',
          title: 'API Reference',
          icon: 'fas fa-code',
          type: 'category',
          subcategories: [
            {
              id: 'getting-started',
              title: 'Getting Started',
              pages: ['using-the-rest-api'],
            },
            {
              id: 'url-management',
              title: 'URL Management',
              pages: ['rest-api-add-url-mapping', 'rest-api-delete-url-mapping', 'rest-api-get-url-mapping'],
            },
            {
              id: 'analytics-reporting',
              title: 'Analytics & Reporting',
              pages: ['rest-api-get-axero-copilot-conversation-records', 'rest-api-get-search-activity-analytics', 'rest-api-get-search-content-analytics'],
            },
            {
              id: 'system-operations',
              title: 'System Operations',
              pages: ['rest-api-rebuild-index', 'rest-api-update-task'],
            },
          ],
        },
        {
          id: 'troubleshooting',
          title: 'Troubleshooting',
          icon: 'fas fa-wrench',
          type: 'category',
          subcategories: [
            {
              id: 'common-issues',
              title: 'Common Issues',
              pages: ['troubleshooting-guide-for-blurry-images'],
            },
          ],
        },
        {
          id: 'changelog',
          title: 'Changelog',
          icon: 'fas fa-history',
          type: 'category',
          subcategories: [
            {
              id: 'version-history',
              title: 'Version History',
              pages: ['2022-enhancements'],
            },
          ],
        },
      ],
    };
  }

  addPageToNavigation(pageId, pageData) {
    // Auto-categorize based on page metadata or content analysis
    const suggestedCategory = this.suggestCategoryForPage(pageData);

    // Add to navigation config (this would typically be saved to a config file)
    const config = this.getNavigationConfig();
    const category = config.categories.find((cat) => cat.id === suggestedCategory.categoryId);

    if (category && category.subcategories) {
      const subcategory = category.subcategories.find((sub) => sub.id === suggestedCategory.subcategoryId);
      if (subcategory) {
        subcategory.pages.push(pageId);
        // Re-generate navigation
        this.initializeNavigation();
      }
    }
  }

  suggestCategoryForPage(pageData) {
    const title = pageData.title.toLowerCase();
    const content = pageData.content?.toLowerCase() || '';

    // Simple categorization logic based on keywords
    if (title.includes('api') || title.includes('rest') || title.includes('endpoint')) {
      if (title.includes('url') || title.includes('mapping')) {
        return { categoryId: 'api-reference', subcategoryId: 'url-management' };
      } else if (title.includes('analytics') || title.includes('search') || title.includes('copilot')) {
        return { categoryId: 'api-reference', subcategoryId: 'analytics-reporting' };
      } else if (title.includes('rebuild') || title.includes('update') || title.includes('task')) {
        return { categoryId: 'api-reference', subcategoryId: 'system-operations' };
      } else {
        return { categoryId: 'api-reference', subcategoryId: 'getting-started' };
      }
    } else if (title.includes('journey')) {
      return { categoryId: 'tutorials-guides', subcategoryId: 'journey-management' };
    } else if (title.includes('saml') || title.includes('sso') || title.includes('auth')) {
      return { categoryId: 'authentication-security', subcategoryId: 'sso-authentication' };
    } else if (title.includes('troubleshoot') || title.includes('error') || title.includes('fix')) {
      return { categoryId: 'troubleshooting', subcategoryId: 'common-issues' };
    } else if (title.includes('config') || title.includes('setup') || title.includes('enable')) {
      if (title.includes('email') || title.includes('notification')) {
        return { categoryId: 'setup-configuration', subcategoryId: 'email-settings' };
      } else {
        return { categoryId: 'setup-configuration', subcategoryId: 'api-setup' };
      }
    } else if (title.includes('wiki') || title.includes('content') || title.includes('file')) {
      return { categoryId: 'tutorials-guides', subcategoryId: 'content-management' };
    } else if (title.includes('webhook') || title.includes('integration')) {
      return { categoryId: 'tutorials-guides', subcategoryId: 'integration' };
    } else if (title.includes('changelog') || title.includes('enhancement') || title.includes('version')) {
      return { categoryId: 'changelog', subcategoryId: 'version-history' };
    }

    // Default fallback
    return { categoryId: 'tutorials-guides', subcategoryId: 'content-management' };
  }

  getCategoryLandingPage(categoryId) {
    const config = this.getNavigationConfig();
    const category = config.categories.find((cat) => cat.id === categoryId);

    if (category && category.subcategories && category.subcategories.length > 0) {
      // Return the first page of the first subcategory as landing page
      const firstSubcategory = category.subcategories[0];
      if (firstSubcategory.pages && firstSubcategory.pages.length > 0) {
        return firstSubcategory.pages[0];
      }
    }

    // Fallback mapping for legacy categories
    const categoryPages = {
      'setup-configuration': 'enabling-api-access',
      'authentication-security': 'saml-2-0-sso',
      'tutorials-guides': 'journeys',
      'api-reference': 'using-the-rest-api',
      troubleshooting: 'troubleshooting-guide-for-blurry-images',
      changelog: '2022-enhancements',
      'api-endpoints': 'using-the-rest-api',
      'examples-tutorials': 'create-journey',
      authentication: 'saml-2-0-sso',
      configuration: 'how-to-enable-images-in-emails',
      endpoints: 'using-the-rest-api',
    };

    return categoryPages[categoryId] || 'introduction';
  }

  addToRecentPages(pageName) {
    const pageTitle = this.getPageTitle(pageName);
    const recentItem = { id: pageName, title: pageTitle, timestamp: Date.now() };

    // Remove if already exists
    this.recentPages = this.recentPages.filter((page) => page.id !== pageName);

    // Add to beginning
    this.recentPages.unshift(recentItem);

    // Keep only last 5
    this.recentPages = this.recentPages.slice(0, 5);

    // Save to localStorage
    localStorage.setItem('recentPages', JSON.stringify(this.recentPages));

    // Update UI
    this.updateRecentPages();
  }

  updateRecentPages() {
    const recentList = document.getElementById('recentList');
    if (!recentList) return;

    if (this.recentPages.length === 0) {
      recentList.innerHTML = '<li><span style="color: var(--text-muted); font-size: 0.75rem;">No recent pages</span></li>';
      return;
    }

    recentList.innerHTML = this.recentPages
      .map(
        (page) => `
            <li><a href="#${page.id}" data-page="${page.id}">${page.title}</a></li>
        `
      )
      .join('');

    // Add click handlers
    recentList.querySelectorAll('a').forEach((link) => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const page = link.dataset.page;
        if (page) {
          this.loadPage(page);
          this.setActiveNavLink(document.querySelector(`[data-page="${page}"]`));
        }
      });
    });
  }

  setupPageNavigation() {
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');

    prevPage?.addEventListener('click', (e) => {
      e.preventDefault();
      this.navigateToPreviousPage();
    });

    nextPage?.addEventListener('click', (e) => {
      e.preventDefault();
      this.navigateToNextPage();
    });
  }

  navigateToPreviousPage() {
    const currentIndex = this.pageOrder.indexOf(this.currentPage);
    if (currentIndex > 0) {
      const prevPage = this.pageOrder[currentIndex - 1];
      this.loadPage(prevPage);
      this.setActiveNavLink(document.querySelector(`[data-page="${prevPage}"]`));
    }
  }

  navigateToNextPage() {
    const currentIndex = this.pageOrder.indexOf(this.currentPage);
    if (currentIndex < this.pageOrder.length - 1) {
      const nextPage = this.pageOrder[currentIndex + 1];
      this.loadPage(nextPage);
      this.setActiveNavLink(document.querySelector(`[data-page="${nextPage}"]`));
    }
  }

  updatePageNavigation() {
    const currentIndex = this.pageOrder.indexOf(this.currentPage);
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');

    // Update previous page
    if (currentIndex > 0) {
      const prevPageId = this.pageOrder[currentIndex - 1];
      const prevTitle = this.getPageTitle(prevPageId);
      prevPage.style.display = 'flex';
      prevPage.querySelector('.page-nav-title').textContent = prevTitle;
    } else {
      prevPage.style.display = 'none';
    }

    // Update next page
    if (currentIndex < this.pageOrder.length - 1) {
      const nextPageId = this.pageOrder[currentIndex + 1];
      const nextTitle = this.getPageTitle(nextPageId);
      nextPage.style.display = 'flex';
      nextPage.querySelector('.page-nav-title').textContent = nextTitle;
    } else {
      nextPage.style.display = 'none';
    }
  }

  setupFeedbackSystem() {
    const feedbackButtons = document.querySelectorAll('.feedback-btn');
    const feedbackForm = document.getElementById('feedbackForm');
    const submitFeedback = document.getElementById('submitFeedback');
    const cancelFeedback = document.getElementById('cancelFeedback');

    feedbackButtons.forEach((btn) => {
      btn.addEventListener('click', () => {
        feedbackButtons.forEach((b) => b.classList.remove('selected'));
        btn.classList.add('selected');

        if (btn.dataset.feedback === 'negative') {
          feedbackForm.style.display = 'block';
        } else {
          feedbackForm.style.display = 'none';
          this.submitFeedbackData(btn.dataset.feedback, '');
        }
      });
    });

    submitFeedback?.addEventListener('click', () => {
      const feedbackText = document.getElementById('feedbackText').value;
      this.submitFeedbackData('negative', feedbackText);
      feedbackForm.style.display = 'none';
    });

    cancelFeedback?.addEventListener('click', () => {
      feedbackForm.style.display = 'none';
      feedbackButtons.forEach((b) => b.classList.remove('selected'));
    });
  }

  submitFeedbackData(type, text) {
    // In a real implementation, this would send data to your analytics service
    console.log('Feedback submitted:', { type, text, page: this.currentPage });

    // Show thank you message
    const feedbackSection = document.getElementById('feedbackSection');
    const originalContent = feedbackSection.innerHTML;
    feedbackSection.innerHTML = `
            <div style="text-align: center; color: var(--success-color);">
                <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
                <p>Thank you for your feedback!</p>
            </div>
        `;

    setTimeout(() => {
      feedbackSection.innerHTML = originalContent;
      this.setupFeedbackSystem();
    }, 3000);
  }

  addCopyButtons() {
    document.querySelectorAll('pre code').forEach((codeBlock) => {
      const pre = codeBlock.parentElement;
      if (pre.querySelector('.copy-code-btn')) return; // Already has button

      const wrapper = document.createElement('div');
      wrapper.className = 'code-block-wrapper';

      const header = document.createElement('div');
      header.className = 'code-block-header';

      const language = codeBlock.className.match(/language-(\w+)/)?.[1] || 'text';
      header.innerHTML = `
                <span class="code-language">${language}</span>
                <button class="copy-code-btn" title="Copy code">
                    <i class="fas fa-copy"></i> Copy
                </button>
            `;

      pre.parentNode.insertBefore(wrapper, pre);
      wrapper.appendChild(header);
      wrapper.appendChild(pre);

      const copyBtn = header.querySelector('.copy-code-btn');
      copyBtn.addEventListener('click', () => {
        navigator.clipboard.writeText(codeBlock.textContent).then(() => {
          copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
          copyBtn.classList.add('copied');
          setTimeout(() => {
            copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy';
            copyBtn.classList.remove('copied');
          }, 2000);
        });
      });
    });
  }

  getPageContent(pageName) {
    const pages = {
      introduction: `<h1 id="introduction">Welcome to Axero Documentation</h1>
<p>Welcome to the comprehensive Axero documentation. This guide contains 25 detailed articles covering all aspects of the Axero platform.</p>

<div class="callout callout-info">
  <div class="callout-title">
    <i class="fas fa-info-circle"></i>
    Getting Started
  </div>
  <p>Browse the navigation menu on the left to explore different topics, or use the search function to find specific information.</p>
</div>

<h2 id="documentation-overview">Documentation Overview</h2>
<div class="doc-overview">
  <div class="overview-section">
    <h3><i class="fas fa-code"></i> API Endpoints</h3>
    <p>9 comprehensive REST API documentation pages</p>
  </div>
  <div class="overview-section">
    <h3><i class="fas fa-book"></i> Examples & Tutorials</h3>
    <p>10 step-by-step guides and tutorials</p>
  </div>
  <div class="overview-section">
    <h3><i class="fas fa-shield-alt"></i> Authentication</h3>
    <p>3 authentication and security guides</p>
  </div>
  <div class="overview-section">
    <h3><i class="fas fa-cog"></i> Configuration</h3>
    <p>1 system configuration guide</p>
  </div>
  <div class="overview-section">
    <h3><i class="fas fa-wrench"></i> Troubleshooting</h3>
    <p>1 problem-solving guide</p>
  </div>
  <div class="overview-section">
    <h3><i class="fas fa-history"></i> Changelog</h3>
    <p>1 version updates and enhancements</p>
  </div>
</div>`,
      'how-to-enable-images-in-emails': `<h1 id="how-to-enable-images-in-emails">How to Enable Images in Emails</h1>
<p>To enable images in email notifications, follow these steps:</p> <ol> <li>Log in to your intranet site using an administrator account with system configuration permissions.</li> <li>Access the system properties by navigating to <strong>Control Panel &gt; System &gt; System Properties</strong>.<br><img src="https://my.axerosolutions.com/attachment?file=PN4QXo3aaFUwipGyQfKqkg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" style="margin: 8px 0 4px" width="800" data-action="zoom"><br><img src="https://my.axerosolutions.com/attachment?file=2AE18LRXtlR4gx53UM7d0A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" style="margin: 8px 0 4px" width="800" data-action="zoom"></li> <li>In the search box, type <strong>ImagesInEmail</strong> to quickly locate the property.<br><img src="https://my.axerosolutions.com/attachment?file=4tpd9zlmBfAdrHGBTunUvg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" style="margin: 8px 0 4px" width="800" data-action="zoom"></li> <li>Click <strong>Edit</strong>, enable the property, and click <strong>Save</strong>.</li> </ol> <p><strong>Important Security Note:</strong> When this feature is enabled, the system will add encrypted information to image URLs to verify access permissions. This may not be suitable for organizations with less secure email systems or high-security environments.</p> <p>If you don't see the <strong>ImagesInEmail </strong>system property, <a href="https://my.axerosolutions.com/spaces/77/communifire-support/cases/add-edit-case/0">submit a support case</a> to request an upgrade to the latest version, which includes this feature.</p>`,
      'troubleshooting-guide-for-blurry-images': `<h1 id="troubleshooting-guide-for-blurry-images">Troubleshooting Guide for Blurry Images</h1>
<p>When implementing a photo carousel, it's crucial to ensure that the correct image URL is used to prevent blurry or improperly displayed images.</p> <h3>Understanding Image URL Variables in Page Builder Widgets</h3> <ul> <li> <p><code>{{ContentImageURL}}</code>: This variable displays the featured image for various content types, such as articles, blogs, and wikis. For photo content types, it displays a thumbnail image.</p> </li> <li> <p><code>{{ContentDetailImageURL}}</code>: This variable provides the path to the full-resolution version of a photo. This URL should be used when displaying images in a carousel template to ensure they appear sharp and clear.</p> </li> </ul> <h3>Common Issues: Blurry Photos in Carousel</h3> <p>If photos appear blurry in your carousel, it may be due to the use of <code>{{ContentImageURL}}</code> instead of <code>{{ContentDetailImageURL}}</code>. The <code>{{ContentImageURL}}</code> displays a smaller, lower-resolution image, which is unsuitable for larger carousel displays. To resolve this issue:</p> <ol> <li><strong>Check the Template</strong>: Review the carousel template to ensure it is designed to distinguish between photo content types and other types of content.</li> <li><strong>Modify the Template</strong>: If the carousel template does not differentiate between content types, modify it to: <ul> <li>Use <code>{{ContentDetailImageURL}}</code> for photo content types to display the full-resolution image.</li> <li>Use <code>{{ContentImageURL}}</code> for other content types where a featured image or thumbnail is appropriate.</li> </ul> </li> </ol> <h3>Correcting Thumbnail Image Dimensions</h3> <p>If thumbnail images appear stretched or have the wrong aspect ratio, it's likely due to incorrect dimension settings.</p> <p>For example, the below settings create portrait-oriented thumbnails that don't fit into landscape display boxes, causing distortion:</p> <ul> <li style="list-style-type: none"> <ul> <li>Thumbnail height (px): 225</li> <li>Thumbnail width (px): 300</li> </ul> </li> </ul> <p>These dimensions create portrait-oriented thumbnails, which do not fit appropriately into landscape-oriented display boxes. This mismatch leads to stretched or distorted images.&nbsp;</p> <h4>Steps to Resolve</h4> <ol> <li> <p><strong>Update Thumbnail Settings:</strong></p> <ul> <li>Navigate to the Photo Management Settings page: <strong>https://your-site-url.com/admin/photos/manage-photo-settings</strong></li> <li>Modify the thumbnail dimensions to: <ul> <li>Content width: 900</li> <li>Thumbnail height (px): 500</li> <li>Thumbnail width (px): 800</li> </ul> </li> </ul> </li> <li> <p><strong>Verify the Change</strong>:</p> <ul> <li>After updating the settings, upload new images to verify that the thumbnails render correctly.</li> </ul> </li> <li> <p><strong>Check the Thumbnails</strong>:</p> <ul> <li>Review the images in albums and photo lists to confirm that the thumbnails display with the correct aspect ratio and fit within the viewport as intended.</li> </ul> </li> </ol> <p>Following these steps should resolve the issue of distorted thumbnails, ensuring images are displayed clearly and properly across the site.</p> <p>Refer to <span class="mceNonEditable">{{mention:79243:9}}</span> to learn more about image settings.</p> <p>&nbsp;</p>`,
      'rest-api-rebuild-index': `<h1 id="rest-api-rebuild-index">REST API: Rebuild Index</h1>
<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Rebuild index</h1> <h3 class="method-url">POST /api/admin/buildindex</h3> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <p class="method-desc">Rebuild the search index.&nbsp;</p> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP&nbsp;Method</span></div> <div class="span2"><span class="label label-success type">POST</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span><span class="desc"></span></div> </div> </div> </div> <table class="table table-striped" style="width: 100%; height: 194px"> <thead> <tr style="height: 40px"> <th class="name" style="width: 15.0983%; height: 40px">Required Parameters</th> <th class="use" style="width: 31.184%; height: 40px">How to use</th> <th class="desc" style="width: 48.5894%; height: 40px">Description</th> </tr> </thead> <tbody> <tr style="height: 154px"> <td style="width: 15.0983%; height: 154px">API Key</td> <td style="width: 31.184%; height: 154px">Used in the request URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td style="width: 48.5894%; height: 154px">Axero REST API key for the Axero portal you are making the call for.</td> </tr> <tr style="height: 80px"> <td style="width: 17.6737%; height: 80px">EntityTypeCSV</td> <td style="width: 32.1752%; height: 80px">Used in the request body&nbsp;</td> <td style="width: 50.1511%; height: 80px">A comma-separated string of EntityTypeIDs to rebuild. Check the list of Entity types in <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/25112/rest-api-system-entity-types">REST API: System Entity Types</a></strong>.</td> </tr> </tbody> </table> <table class="table table-striped" style="width: 100.302%; height: 195px"> <thead> <tr style="height: 40px"> <th class="name" style="width: 18.1269%; height: 40px">Optional Parameters</th> <th class="use" style="width: 34.7432%; height: 40px">How to use</th> <th class="desc" style="width: 47.1299%; height: 40px">Description</th> </tr> </thead> <tbody> <tr style="height: 80px"> <td style="width: 18.1269%; height: 80px">IsRebuildAll</td> <td style="width: 34.7432%; height: 80px">&amp;IsRebuildAll=true/false - Used in the request URL as a query parameter</td> <td style="width: 47.1299%; height: 80px"> <p>Set it to <em>true</em> to rebuild the entire index; ensure that&nbsp;<strong>all</strong> entity types are included in EntityTypeCSV for a complete index update.<br>Set it to <em>false</em> to reindex only the specified entity types included in the request body.</p> </td> </tr> </tbody> </table> <h3>Example Request</h3> <p><span style="font-size: 12pt">POST</span> <a href="#">https://myintranet.communifire.com/api/admin/buildindex?IsRebuildAll=false</a></p> <p>Body:</p> <pre><code class="json">[3, 4, 5]&nbsp;</code></pre> <h3>True response code is returned if successful</h3> <pre><code class="json">&nbsp;true</code></pre> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>`,
      'rest-api-get-search-activity-analytics': `<h1 id="rest-api-get-search-activity-analytics">REST API: Get Search Activity Analytics</h1>
<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Get Search Activity Analytics</h1> <h3 class="method-url">GET api/search/activity</h3> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <p class="method-desc">Get a list of <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/26816/search-analytics" style="text-decoration: none">Search Activity Analytics</a>&nbsp;such as keywords, user details, timestamps, and engagement metrics.&nbsp;</p> <div class="row-fluid api-info"> <div class="span8 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">HTTP Method</span></div> <div class="span4"><span class="label label-info type">GET</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Response Format</span></div> <div class="span4"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Requires Authentication?</span></div> <div class="span4"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Product Version</span></div> <div class="span4"><span class="type">8.0 and above&nbsp;</span></div> </div> </div> </div> <table class="table table-striped" height="296" style="height: 296px; width: 700px; border-style: none"> <thead> <tr style="height: 40px"> <th class="name" style="height: 40px; width: 114.5px">Required Parameters</th> <th class="use" style="height: 40px; width: 241.547px">How to use</th> <th class="desc" style="height: 40px; width: 272.953px">Description</th> </tr> </thead> <tbody> <tr style="height: 120px"> <td style="height: 120px; width: 114.5px">API Key</td> <td style="height: 120px; width: 241.547px">Used in the request URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td style="height: 120px; width: 272.953px"> <p>Axero REST API key for the Axero portal you are making the call for.</p> </td> </tr> <tr> <td style="width: 114.5px">StartPage</td> <td style="width: 241.547px">&amp;StartPage=x - Used in the request URL (see below)</td> <td style="width: 272.953px">Specifies the starting page of list to be returned.</td> </tr> <tr> <td style="width: 114.5px">PageLength</td> <td style="width: 241.547px">&amp;PageLength=x - Used in the request URL (see below)</td> <td style="width: 272.953px">Specifies the number of results to be returned per page.</td> </tr> </tbody> </table> <table class="table table-striped" style="width: 663px; border-style: none"> <thead> <tr style="height: 40px"> <th class="name" style="height: 40px; width: 108.219px">Optional Parameters</th> <th class="use" style="height: 40px; width: 211.719px">How to use</th> <th class="desc" style="height: 40px; width: 309.062px">Description</th> </tr> </thead> <tbody> <tr style="height: 80px"> <td style="height: 80px; width: 108.219px">SpaceID</td> <td style="height: 80px; width: 211.719px">&amp;SpaceID=x - Used in the request URL</td> <td style="height: 80px; width: 309.062px">Unique ID of the space from which the content is to be returned.<br>Don't pass any value if you want content from every space, where you have access.</td> </tr> <tr style="height: 20px"> <td style="height: 20px; width: 108.219px">StartDate</td> <td style="height: 20px; width: 211.719px">&amp;startDate=x - Used in the request URL</td> <td style="height: 20px; width: 309.062px">Start date of range to Search history. The date format is YYYY-MM-DD:HH:mm:ssZ.&nbsp;</td> </tr> <tr style="height: 20px"> <td style="height: 20px; width: 108.219px">EndDate</td> <td style="height: 20px; width: 211.719px">&amp;endDate=x - Used in the request URL</td> <td style="height: 20px; width: 309.062px">End date of range to Search history. The date format is YYYY-MM-DD:HH:mm:ssZ.&nbsp;</td> </tr> <tr> <td style="width: 108.219px">SearchTerm</td> <td style="width: 211.719px">&amp;term=x - Used in the request URL</td> <td style="width: 309.062px">Specifies the search term to display results.&nbsp;</td> </tr> <tr> <td style="width: 108.219px">Person</td> <td style="width: 211.719px">&amp;UserID=x - Used in the request URL</td> <td style="width: 309.062px">User ID to return results for.&nbsp;</td> </tr> </tbody> </table> <p>&nbsp;</p> <h3>Example Request<br><a href="#"></a></h3> <p><span style="font-size: 14pt">GET</span> <a href="#">https://myintranet.communifire.com/api/search/activity?StartPage=1&amp;PageLength=2</a></p> <h3>Example Response</h3> <p>Content JSON object is returned as ResponseData.&nbsp;</p> <pre>{ "IsError": false, "ResponseMessage": "", "ResponseData": [ { "SearchTerm": "test", "UserID": 950, "UserDisplayName": "Alexis Fox", "UserProfileURL": "https://myintranet.communifire.com/people/<br> Alexis", "DateSearched": "2024-12-12T09:57:30.167", "DateSearchedString": "38 minutes ago", "ClickedEntityID": 0, "ClickedEntityType": 0, "SearchCount": 5, "ClickCount": 3, "ClickThroughRate": 0.8 }, { "SearchTerm": "marketing", "UserID": 943, "UserDisplayName": "Alice Romero", "UserProfileURL": "https://myintranet.communifire.com/people/<br> Alice", "DateSearched": "2024-12-12T09:57:24.51", "DateSearchedString": "50 minutes ago", "ClickedEntityID": 0, "ClickedEntityType": 0, "SearchCount": 3, "ClickCount": 1, "ClickThroughRate": 0.1 }<br>] }</pre> <p>&nbsp;</p> <h3>Example Request for searching with Search Term and Person<br><a href="#"></a></h3> <p><span style="font-size: 14pt">GET</span>&nbsp;<a href="#">https://myintranet.communifire.com/api/activityStartPage=1&amp;PageLength=2&amp;term=test&amp;UserID=950</a></p> <h3>Example Response</h3> <p>Content JSON object is returned as ResponseData.&nbsp;</p> <pre>{ "IsError": false, "ResponseMessage": "", "ResponseData": [ { "SearchTerm": "test", "UserID": 950, "UserDisplayName": "Alexis Fox", "UserProfileURL": "https://myintranet.communifire.com/people/<br> Alexis", "DateSearched": "2024-12-12T09:57:30.167", "DateSearchedString": "an hour ago", "ClickedEntityID": 0, "ClickedEntityType": 0, "SearchCount": 3, "ClickCount": 5, "ClickThroughRate": 0.8 }, { "SearchTerm": "testing", "UserID": 950, "UserDisplayName": "Alexis Fox", "UserProfileURL": "https://myintranet.communifire.com/people/<br> Alexis", "DateSearched": "2024-08-12T18:19:10.703", "DateSearchedString": "12/8/2024", "ClickedEntityID": 0, "ClickedEntityType": 0, "SearchCount": 8, "ClickCount": 1, "ClickThroughRate": 0.1 } ] }</pre> <br> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>`,
      'rest-api-get-search-content-analytics': `<h1 id="rest-api-get-search-content-analytics">REST API: Get Search Content Analytics</h1>
<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Get Search Content Analytics</h1> <h3 class="method-url">GET api/search/content</h3> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <p class="method-desc">Get a list of&nbsp;<a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/97007/analytics-search#content" style="text-decoration: none">Search Content Analytics</a>, such as search terms, content titles, and click counts for each entry.&nbsp;</p> <div class="row-fluid api-info"> <div class="span8 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">HTTP Method</span></div> <div class="span4"><span class="label label-info type">GET</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Response Format</span></div> <div class="span4"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Requires Authentication?</span></div> <div class="span4"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Product Version</span></div> <div class="span4"><span class="type">8.0 and above&nbsp; &nbsp;</span></div> </div> </div> </div> <table class="table table-striped" height="296" style="height: 193.717px; width: 653px; border-style: none"> <thead> <tr style="height: 48.2639px"> <th class="name" style="width: 148.464px">Required Parameters</th> <th class="use" style="width: 248.021px">How to use</th> <th class="desc" style="width: 224.288px">Description</th> </tr> </thead> <tbody> <tr style="height: 145.453px"> <td style="width: 148.464px">API Key</td> <td style="width: 248.021px">Used in the request URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td style="width: 224.288px">Axero REST API key for the Axero portal you are making the call for.</td> </tr> </tbody> </table> <table class="table table-striped" style="width: 663px; border-style: none; height: 645.139px"> <thead> <tr style="height: 48.2639px"> <th class="name" style="width: 108.219px">Optional Parameters</th> <th class="use" style="width: 211.719px">How to use</th> <th class="desc" style="width: 309.062px">Description</th> </tr> </thead> <tbody> <tr style="height: 68.5417px"> <td style="width: 114.5px">StartPage</td> <td style="width: 241.547px">&amp;StartPage=x - Used in the request URL (see below)</td> <td style="width: 272.953px">Specifies the starting page of list to be returned (defaults to 1 if not provided).</td> </tr> <tr style="height: 48.5417px"> <td style="width: 114.5px">PageLength</td> <td style="width: 241.547px">&amp;PageLength=x - Used in the request URL (see below)</td> <td style="width: 272.953px">Specifies the number of results to be returned per page. (defaults to 20)</td> </tr> <tr style="height: 108.542px"> <td style="width: 108.219px">SpaceID</td> <td style="width: 211.719px">&amp;SpaceID=x - Used in the request URL</td> <td style="width: 309.062px">Unique ID of the space from which the content is to be returned.<br>Don't pass any value if you want content from every space, where you have access.</td> </tr> <tr style="height: 68.5417px"> <td style="width: 108.219px">StartDate</td> <td style="width: 211.719px">&amp;startDate=x - Used in the request URL</td> <td style="width: 309.062px">Start date of range to Search history. The date format is YYYY-MM-DD:HH:mm:ssZ.&nbsp;</td> </tr> <tr style="height: 68.5417px"> <td style="width: 108.219px">EndDate</td> <td style="width: 211.719px">&amp;endDate=x - Used in the request URL</td> <td style="width: 309.062px">End date of range to Search history. The date format is YYYY-MM-DD:HH:mm:ssZ.&nbsp;</td> </tr> <tr style="height: 68.5417px"> <td style="width: 108.219px">SearchTerm</td> <td style="width: 211.719px">&amp;searchTerm=x - Used in the request URL</td> <td style="width: 309.062px"> <p>Specifies the search term to display results.&nbsp;</p> </td> </tr> <tr style="height: 48.5417px"> <td style="width: 108.219px">EntityTypeID</td> <td style="width: 211.719px">&amp;EntityTypeID=x - Used in the request URL</td> <td style="width: 309.062px">The entity type ID of the content to be returned. Here are the values of EntityTypeID parameter: <ul style="list-style-type: disc"> <li>Forum = 1</li> <li>ForumGroup = 2</li> <li>Article = 3</li> <li>Blog = 4</li> <li>Photo = 6</li> <li>Video = 7</li> <li>Wiki = 9</li> <li>CMSPage = 12</li> <li>File = 14</li> <li>Album = 18</li> <li>Idea = 44</li> <li>ForumPost = 54</li> <li>ForumTopic = 55</li> </ul> </td> </tr> <tr style="height: 48.5417px"> <td style="width: 108.219px">noOfSearchTermResult</td> <td style="width: 211.719px">&amp;noOfSearchTermResult=x - Used in the request URL</td> <td style="width: 309.062px">Specifies the maximum number of search terms to include for each piece of content (defaults to 20).</td> </tr> </tbody> </table> <p>&nbsp;</p> <h3>Example Request<br><a href="#"></a></h3> <p><span style="font-size: 12pt"><span style="font-size: 14pt">GET&nbsp;</span><a href="#">https://myintranet.communifire.com/api/search/content?SpaceID=252<br>&amp;PageLength=2&amp;EntityTypeID=3</a></span></p> <h3>Example Response</h3> <p>Content JSON object is returned as ResponseData.&nbsp;</p> <pre>[ { "ContentID": 6918, "EntityTypeID": 3, "ContentTypeID": 3, "ContentTypeText": "Article", "ContentTitle": "Open Enrollment Starts Today! Time to Choose Your <br> Health Plan for This Upcoming Year", "SpaceID": 252, "SpaceName": "Human Resources", "ClickCount": 25, "SearchTerms": "a,benefits,open enroll,open enrollment" }, { "ContentID": 6646, "EntityTypeID": 3, "ContentTypeID": 3, "ContentTypeText": "Article", "ContentTitle": "The Value of Processes and Procedures in the Work <br> place", "SpaceID": 252, "SpaceName": "Human Resources", "ClickCount": 32, "SearchTerms": "value,work" }<br>]</pre> <p>&nbsp;</p> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> <p>&nbsp;</p> </div> </div> </div> </div> </div>`,
      '2022-enhancements': `<h1 id="2022-enhancements">2022 Enhancements</h1>
<h3 id="mcetoc_1gnflf823f">December 2022</h3> <ul> <li>Enhance your Microsoft Teams and Axero experience and <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/92227/embed-axero-as-an-app-in-microsoft-teams">Embed Axero as an App in Microsoft Teams</a></strong></li> <li>Send <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/92889/browser-notifications">Browser notifications</a></strong> through different browsers to keep your users up to date even when they are on actively on your site.</li> <li>Enhance your content by adding featured images for events through our <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/92908/rest-api-add-event-featured-image">REST API: Add Event Featured Image</a></strong></li> <li>Update event attendees through our new <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/92910/rest-api-add-users-to-an-event-as-attendees">Rest API: Add Users to an Event as Attendees</a></strong></li> </ul> <h3 id="mcetoc_1gnflf823g">November 2022</h3> <ul> <li>Add a user's profile information into Page Builder pages using our new<strong> </strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/92840/profile-field-widget"><strong>Profile Field Widget</strong>.</a></li> <li>Assign tags when adding files through <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/4879/rest-api-add-file">REST API: Add File</a></strong> with new optional parameter "tags".</li> </ul> <h3 id="mcetoc_1gnflf823h">October 2022</h3> <ul> <li>Users can now <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21520/my-bookmarks">bookmark</a> </strong>any page on their site.</li> <li>Administrators can set <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21630/password">Password expiration</a></strong>.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/2115/photos">Photos and Photo albums</a></strong> can now be sorted Alphabetically.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22190/email-settings#o365Email">Email settings</a></strong> for sending emails via an O365 account have been updated.</li> <li>Sending times for <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22683/weekly-digest-email#mcetoc_1elo3vsbu1">weekly</a> </strong>and <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22682/daily-digest-email#mcetoc_1elo492412">daily digest</a> </strong>emails can be set by administrators.</li> <li>Confirmed reading can now be used for <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22169/add-badge-challenge">badge challenge</a></strong>.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21483/create-forum">Creating a forum</a></strong> in a Space can now be done without navigating to the Space control panel.</li> <li>Parent URL now returned for content in <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/595/rest-api-get-content-list">REST API: Get Content List</a></strong>.</li> </ul> <h3 id="mcetoc_1gnflf823i">September 2022</h3> <ul> <li>Mobile layout now has <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/78459/dark-mode">Dark Mode</a> </strong>available.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22589/people-widget">People Widget</a></strong> now can be now be built with Personas and multiple users widget template.</li> <li>UI update for <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/89023/notifications-for-content">Notify People</a></strong> functionality</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21130/create-wiki-page">"Publication Date"</a></strong> is now available for Wiki Content Type</li> </ul> <h3 id="mcetoc_1gnflf823j">August 2022</h3> <ul> <li>Persona Actions now include <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/86770/personas-include-actions"><strong>assigning space roles</strong>.</a></li> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21520/my-bookmarks"><strong>Bookmarks</strong></a> can now be sorted.</li> <li>Individual events can now be edited or deleted in a <span style="text-decoration: underline"><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21478/events">recurring calendar event.</a></strong></span></li> <li>Update to <strong><span style="text-decoration: underline"><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/38400/recognition">Recognition Leaderboard</a></span> </strong>to include Challenges.</li> <li><span style="text-decoration: underline"><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/26794/outlook-sync-administrator-setup">Outlook integration</a></strong></span> updated to manage recurring calendar events.</li> <li>Update a user profile field with <span style="text-decoration: underline"><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/27247/rest-api-update-user-profile-fields">multiple values via API</a>.</strong></span></li> <li>Views of Page Builder pages are now logged in <strong><span style="text-decoration: underline"><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/23907/audience-overview">analytics</a>.</span></strong></li> </ul> <h3 id="mcetoc_1gnflf823k">July 2022</h3> <ul> <li>Unique Viewers column have been added to the <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/38462/top-content">Top Content</a></strong> CSV download.</li> <li>Users can now <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21070/required-reading">send reminder notifications for required reading content</a></strong>.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/88998/personas-in-spaces">Multiple Personas</a></strong> can be added to Spaces.</li> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/4652/convert-resources-change-the-language-of-your-intranet"><strong>Professional translation of System Resources</strong></a><strong> </strong>is now available for Italian and Portuguese</li> <li>Ability to sync <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/42136/okta-sso#OktaGroupingCONTENT">Groups from Okta to Axero</a></strong></li> </ul> <h3 id="mcetoc_1gnflf823l">June 2022</h3> <ul> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22597/search-widget">Search Widget</a></strong> is now available on mobile page builder.</li> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22551/raw-html-widget"><strong>Raw HTML Widget</strong></a> is now available on mobile mobile page builder.</li> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21519/connect-email-to-calendar"><strong>Default Outlook email account</strong></a> option to send event details from.</li> <li>Pressing enter in search box will show user results in <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22062/manage-people">Manage People</a></strong></li> </ul> <h3 id="mcetoc_1gnflf823m">May 2022</h3> <ul> <li><span style="text-decoration: underline"><strong>{{mention:90408:9}}</strong></span> available for users.</li> </ul> <h3 id="mcetoc_1gnflf823n">April 2022</h3> <ul> <li><span style="text-decoration: underline"><strong>{{mention:89872:9}}</strong></span> now available.</li> <li><span style="text-decoration: underline"><strong>{{mention:89625:9}}</strong></span> available for users.</li> <li><span style="text-decoration: underline">{{mention:89931:9}}</span> option available.</li> </ul> <h3 id="mcetoc_1gnflf823o">March 2022</h3> <ul> <li><span style="text-decoration: underline"><strong>{{mention:89437:9}} </strong></span>are now available for users.</li> <li><strong><span style="text-decoration: underline">{{mention:21478:9}}</span> </strong>now has an option for Maximum number of attendees.</li> </ul> <h3 id="mcetoc_1gnflf823p">February 2022</h3> <ul> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/88998/personas-in-spaces">Space level personas</a></strong> are now available for creation. (Beta release)</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/89023/notifications-for-content">Custom Notifications</a></strong> are now available for content activity.</li> <li>REST API: Create and automate <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/89109/rest-api-url-mapping-api">multiple URL Redirects</a></strong></li> </ul> <h3 id="mcetoc_1gnflf823q">January 2022</h3> <ul> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/88380/promoted-search" rel="noopener" target="_blank"><strong>Promoted Search</strong></a> option now available for content.</li> <li>Profile pages are an option in <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22188/site-settings">site settings</a> </strong>to be the landing page when users log in.</li> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/88239/force-password-change" rel="noopener" target="_blank"><strong>Force Password Change</strong></a> for users.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/88056/splunk-integration-setup" rel="noopener" target="_blank">Splunk services</a></strong> can now integrate with Axero data.</li> <li>Create sets of words that relate to each other with <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22607/search#mcetoc_dysearchsyn" rel="noopener" target="_blank"><strong>Search Synonyms</strong></a>.</li> </ul>`,
      'setup-guide-onelogin-saml-sso': `<h1 id="setup-guide-onelogin-saml-sso">Setup Guide: OneLogin SAML SSO</h1>
<div id="on-prem-installation"> <p>This guide provides step-by-step instructions to configure OneLogin for Single Sign-On (SSO) with Axero using SAML 2.0. Users will authenticate using their OneLogin credentials, with account management handled in Axero.</p> <h3 style="margin-top: 10px">Overview</h3> <ul style="margin-bottom: 0"> <li><a href="#preparation">Preparation</a></li> <li><a href="#step-1">Step 1: Configure OneLogin</a></li> <li><a href="#step-2">Step 2: Configure Axero</a></li> <li><a href="#step-3">Step 3: Test SSO Integration</a></li> <li><a href="#optional-custom-attributes">Optional: Add Custom Attributes</a></li> <li><a href="#troubleshooting">Troubleshooting</a></li> </ul> <h3 id="preparation">Preparation</h3> <p style="margin-bottom: 8px">Before you start, ensure you have:</p> <ul> <li>Administrator access to your OneLogin portal</li> <li>Site Administrator access to your Axero site</li> <li>Confirmed the following Axero <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22317/system-properties">System Properties</a> are configured: <ul> <li>Go to <strong>Control Panel &gt; System &gt; System Properties</strong></li> <li>EnableAutoLoginViaSaml = False</li> <li>SAMLAutoUserCreation = True</li> <li>SAMLAutoUserUpdate = True</li> <li>SAMLUserEmailMatch = False</li> </ul> </li> <li>Adjusted the following settings for your planned usernames: <ul> <li>Go to <strong>Control Panel &gt; System &gt; General Settings &gt; Advanced Settings</strong>.</li> <li>Update <strong>Minimum length for username</strong> and <strong>Maximum length for username</strong> as needed to accommodate your planned username format.</li> </ul> </li> </ul> <p style="margin-top: 12px">Adjusting these settings will help prevent potential errors during user creation and login.</p> <hr> <h3 id="step-1">Step 1: Configure OneLogin</h3> <ol> <li>Log in to your OneLogin Admin portal.</li> <li>Go to <strong>Applications &gt; Applications</strong> and click <strong>Add App</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=X1R4SKZOo0Im02Ktqo72dQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Search for and select <strong>SCIM Provisioner with SAML (SCIM v2 Core)</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=XJVebRwa%2Fy8YW3rQvtaf7A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Enter a display name for your application (for example, "Axero") and click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=XLEnoYWzFV1g%2BIAQgy81JQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>On the <strong>Configuration</strong> tab, enter the following: <ol style="list-style-type: lower-alpha"> <li><strong>SAML Audience URL</strong>: Enter your Axero site URL (for example, https://your-axero-site.com).</li> <li><strong>SAML Consumer URL</strong>: Enter your Axero site URL followed by <code>/SAML/AssertionConsumerService.aspx</code> (for example, https://your-axero-site.com/SAML/AssertionConsumerService.aspx).</li> <li><strong>SCIM Base URL</strong>: Enter your Axero site URL followed by <code>/api/scim/v2</code> (for example, https://your-axero-site.com/api/scim/v2). OneLogin requires this URL to be set even though you're only setting up SAML SSO now.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=WFlSSy4cMBzOwzrH6rWJfw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=G7De0doXHUnRp%2FtmYMuEQA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> </li> <li>On the <strong>Parameters</strong> tab, configure these required attributes: <ol style="list-style-type: lower-alpha"> <li><strong>First Name</strong> <ol> <li>Click <strong>+</strong> to add a new parameter.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=hIkx1stw4qhMRihSHKsulg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Set <strong>Field name</strong> to "FirstName".</li> <li>Select "Include in SAML assertion".</li> <li>Click <strong>Save</strong>.</li> <li>Select "First Name" from the <strong>Value</strong> dropdown.</li> <li>Click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=uR97Wah4cClc2OTH72CP8g%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> </li> <li><strong>Last Name</strong> <ol> <li>Click <strong>+</strong> to add a new parameter.</li> <li>Set <strong>Field name</strong> to "LastName".</li> <li>Select "Include in SAML assertion".</li> <li>Click <strong>Save</strong>.</li> <li>Select "Last Name" from the <strong>Value</strong> dropdown.</li> <li>Click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=xVytGw%2F3fJ6ZhWEB%2FNDE0g%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> </li> <li><strong>Email</strong> <ol> <li>Click <strong>+</strong> to add a new parameter.</li> <li>Set <strong>Field name</strong> to "Email".</li> <li>Select "Include in SAML assertion".</li> <li>Click <strong>Save</strong>.</li> <li>Select "Email" from the <strong>Value</strong> dropdown.</li> <li>Click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=tOl4pn33fE%2F65hfXwM0hZQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> </li> <li>Click <strong>Save</strong> to save your changes.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=vL4YO0ZbchrQAz7ztvE3LQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> <p class="note"><strong>Note:</strong> These attributes are case-sensitive and must match exactly as shown above for proper mapping in Axero.</p> </li> <li>On the <strong>SSO</strong> tab: <ol style="list-style-type: lower-alpha"> <li>Under <strong>SAML Signature Algorithm</strong>, select <strong>SHA-256</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=M3y2QeR82RApVd2EkWuwPQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Click <strong>Save</strong>.</li> <li>Return to the <strong>SSO</strong> tab.</li> <li>Record the following values: <ul> <li><strong>Issuer URL</strong></li> <li><strong>SAML 2.0 Endpoint (HTTP)</strong></li> <li><strong>SLO Endpoint (HTTP)<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=qRUbycNsX1aHI%2F6Y6OfDGw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"><br></strong></li> </ul> </li> <li>Under <strong>X.509 Certificate</strong>, click <strong>View Details</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=r9MjbWeTrebJV3BWWRDLoA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Under <strong>X.509 Certificate</strong>, select "X.509 PEM" and click <strong>Download</strong> to download the certificate.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=s%2F4Bet3qHXOdwRoxMu9vSw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Share the certificate in your support ticket, and Axero will convert it to the correct format and upload it to your Axero site.</li> </ol> </li> </ol> <h3 id="step-2">Step 2: Configure Axero</h3> <ol> <li>Log in to your Axero site as a Site Administrator.</li> <li>Navigate to <strong>Control Panel &gt; System &gt; Single Sign-On</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=0MCAeiiKKytMrlW90FV1Og%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Select SAML as the authentication method and click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=z5WL2NOAzKXTo8n0yshSSQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Enter the following information from the OneLogin SSO tab: <ul> <li><strong>Partner Identity Provider URL:</strong> Enter the Issuer URL.</li> <li><strong>Single Sign-On Service URL:</strong> Enter the SAML 2.0 Endpoint (HTTP).</li> <li><strong>Relying Party Trust Identifier:</strong> Enter your Axero site URL.</li> <li><strong>Single Logout Service URL:</strong> Enter the SLO Endpoint (HTTP).<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=ppQoN1gmRWJgCYiuaymptg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ul> </li> <li>Click <strong>Update</strong> to save your changes.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=eKK0NtKbpvnvtVloOxDaNg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Click the <strong>Data Mapping</strong> tab and select <strong>SAML</strong> from the dropdown.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=8brtGLsK0UfAGlskZlZWXg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Update the following mappings so that the property names match exactly as shown: <table class="mapping-table"><caption>SAML Attribute Mappings</caption> <thead> <tr> <th scope="col">Property Name</th> <th scope="col">Field</th> </tr> </thead> <tbody> <tr> <td>FirstName</td> <td>First name</td> </tr> <tr> <td>LastName</td> <td>Last name</td> </tr> <tr> <td>Email</td> <td>Email</td> </tr> </tbody> </table> <p class="note"><strong>Note:</strong> These mappings are case-sensitive and must match exactly as shown above for proper mapping in Axero.</p> </li> <li>Delete any other mappings and click <strong>Update</strong> to save your changes.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=XE5NgK2YzXZNSJMeS3kPWw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> <h3 id="step-3">Step 3: Test SSO Integration</h3> <ol> <li>In OneLogin: <ol style="list-style-type: lower-alpha"> <li>Assign yourself to the Axero application. <ol> <li>Go to <strong>Users &gt; Users</strong>.</li> <li>Select yourself from the list.</li> <li>Click the <strong>Applications</strong> tab.</li> <li>Click <strong>+</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=O50Sddt2c5ldSL0%2F8TO8sg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Select <strong>Axero</strong> from the dropdown and click <strong>Continue</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=e%2FbTeiITEnBLtmSY2bTq%2BQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Click <strong>Save</strong>.</li> </ol> </li> <li>Ensure your user profile has the required attributes (first name, last name, email).</li> </ol> </li> <li>Test SSO login: <ol style="list-style-type: lower-alpha"> <li>Follow the <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/101706/how-to-add-an-sso-login-button">instructions</a> for adding a "Login via SSO" button to your login page.</li> <li>Open a new incognito/private browser window.</li> <li>Navigate to your Axero site's login page.</li> <li>Click the <strong>Login via SSO</strong> button.</li> <li>Verify successful authentication and profile data mapping.</li> </ol> </li> </ol> <h3 id="optional-custom-attributes">Optional: Add Custom Attributes</h3> <ol> <li>In OneLogin, go to your application's <strong>Parameters</strong> tab.</li> <li>Click <strong>+</strong> to add a custom attribute.</li> <li>Enter the <strong>Field name</strong> from the following list: <table class="mapping-table"><caption>SAML Attribute Mappings</caption> <thead> <tr> <th scope="col">Field Name</th> <th scope="col">Value</th> </tr> </thead> <tbody> <tr> <td>ProfilePhoto</td> <td>Profile Picture</td> </tr> <tr> <td>Phone</td> <td>Phone</td> </tr> <tr> <td>ManagerEmail</td> <td>Manager Email</td> </tr> <tr> <td>Company</td> <td>Company</td> </tr> <tr> <td>Department</td> <td>Department</td> </tr> <tr> <td>Title</td> <td>Title</td> </tr> </tbody> </table> </li> <li>Select "Include in SAML assertion".</li> <li>Click <strong>Save</strong>.</li> <li>Select the corresponding value for the field name in step 3 from the dropdown.</li> <li>Repeat steps 2-6 for each custom attribute.</li> <li>Click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=0%2BsSbWJA8kvdqubfDwFIkw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>In Axero, go to <strong>Control Panel &gt; System &gt; Single Sign-On &gt; Data Mapping</strong>.</li> <li>Select <strong>SAML</strong> from the dropdown.</li> <li>Click <strong>Add</strong> to add a new mapping.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=ZgVDlzsSV90EtvaOuhBhDA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Add a mapping for each desired attribute, ensuring the property names match precisely: <table class="mapping-table"><caption>SAML Attribute Mappings</caption> <thead> <tr> <th scope="col">Property Name</th> <th scope="col">Field</th> </tr> </thead> <tbody> <tr> <td>ProfilePhoto</td> <td>Profile photo</td> </tr> <tr> <td>Phone</td> <td>Phone</td> </tr> <tr> <td>ManagerEmail</td> <td>Reports to</td> </tr> <tr> <td>Company</td> <td>Company</td> </tr> <tr> <td>Department</td> <td>Department</td> </tr> <tr> <td>Title</td> <td>Occupation</td> </tr> </tbody> </table> </li> <li>Click <strong>Update</strong> to save your changes.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=djWJrpbtYQfHsXIO9p7jFQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Test the new attribute mappings by logging in via SSO.</li> </ol> <hr> <h3 id="troubleshooting">Troubleshooting</h3> <ul> <li><strong>User Deprovisioning:</strong> OneLogin SAML integration does not automatically deactivate users in Axero. Deactivate users manually in Axero or use SCIM provisioning.</li> <li><strong>Role Mapping:</strong> To map OneLogin roles/groups to Axero roles, use custom attributes and configure them in the Data Mapping section.</li> </ul> </div>`,
      'file-field': `<h1 id="file-field">File Field</h1>
<p>Use a file field to allow users to upload and manage files in user profiles. You can specify the types of files allowed, the maximum file upload size, and the number of files a user can upload.</p> <h2><img alt="File Field" src="https://my.axerosolutions.com/attachment?file=v9I4J7trhuCwu31lzh1gFA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="662" width="1094" data-action="zoom"></h2> <h2>Properties</h2> <table> <tbody> <tr> <td style="width: 50%"><strong>Property</strong></td> <td style="width: 50%"><strong>Description</strong></td> </tr> <tr> <td style="width: 50%">Field label</td> <td style="width: 50%">The field label appears at the top of the field. Start typing, and a list of matching text resources will appear. Select one to use or finish typing to create a new resource when the page is saved.</td> </tr> <tr> <td style="width: 50%">Number required</td> <td style="width: 50%">The minimum number of files a user is required to provide.</td> </tr> <tr> <td style="width: 50%">Number permitted</td> <td style="width: 50%">The maximum number of files a user can upload (typically 1). Enter <code>0</code> for unlimited uploads.</td> </tr> <tr> <td style="width: 50%">Permitted file types</td> <td style="width: 50%">A list of file types allowed for upload. Provide a comma-separated list of acceptable file extensions (without the dot, e.g., "pdf, docx, png") to specify the file types allowed for upload. Leave blank to use the site-wide permitted file types or to allow all file types.</td> </tr> <tr> <td style="width: 50%">Maximum file upload size (MB)</td> <td style="width: 50%">The maximum size of a single file allowed for upload, in megabytes.</td> </tr> </tbody> </table> <h2>Field Help</h2> <p>Text to help users understand what is expected when uploading a file. For example: "Please upload a document no larger than 10MB. Allowed formats: PDF, DOCX."</p> <h2>Advanced</h2> <table style="width: 100%; height: 150.844px"> <tbody> <tr style="height: 31.9688px"> <td style="width: 50%"><strong>Field</strong></td> <td style="width: 50%"><strong>Description</strong></td> </tr> <tr style="height: 54.9375px"> <td style="width: 50%">Custom CSS class</td> <td style="width: 50%">A CSS class to append to the field to apply custom styling.</td> </tr> <tr style="height: 31.9688px"> <td style="width: 50%">Show Label</td> <td style="width: 50%">Check to show the field's label.</td> </tr> <tr style="height: 31.9688px"> <td style="width: 50%">Create/Update</td> <td style="width: 50%">Select whether the field should appear only when creating new content or only when updating existing content.</td> </tr> </tbody> </table> <h2>Roles</h2> <p>Choose the roles that can view this field.<strong></strong></p>`,
      'rest-api-update-task': `<h1 id="rest-api-update-task">REST API: Update Task</h1>
<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Update Task</h1> <h3 class="method-url">PUT /api/tasks/<span class="api-param">{TaskID}</span></h3> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <p class="method-desc">Update a task.</p> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP Method</span></div> <div class="span2"><span class="label label-warning type">PUT</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span></div> </div> </div> </div> <table class="table table-striped"> <thead> <tr> <th class="name">Required Parameters</th> <th class="use">How to use</th> <th class="desc">Description</th> </tr> </thead> <tbody> <tr> <td>API Key</td> <td>Used in the request URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td>Axero REST API key for the Axero portal you are making the call for.</td> </tr> <tr> <td>TaskID</td> <td>Used in the request URL as a path parameter</td> <td>Unique ID of the task.</td> </tr> <tr> <td>Task JSON</td> <td>Used in the request body</td> <td>JSON&nbsp;representing the task.</td> </tr> </tbody> </table> <table class="table"> <thead> <tr> <th class="name">Optional Parameters</th> <th class="use">How to use</th> <th class="desc">Description</th> </tr> </thead> <tbody> <tr> <td>TaskName</td> <td>Used in the request body</td> <td>The task name.</td> </tr> <tr> <td>TaskListID</td> <td>Used in the request body</td> <td>ID of the task list to add the task to.</td> </tr> <tr> <td>SpaceID</td> <td>Used in the request body</td> <td>ID of the space the task list is in.</td> </tr> <tr> <td>TaskDescription</td> <td>Used in the request body</td> <td>The task description.</td> </tr> <tr> <td>AssignedToUserID</td> <td>Used in the request body</td> <td>ID of the user to assign the task to.</td> </tr> <tr> <td>TaskStatusID</td> <td>Used in the request body</td> <td>ID of the task status to set.</td> </tr> <tr> <td>TaskPriorityID</td> <td>Used in the request body</td> <td>ID of the task priority to set.</td> </tr> <tr> <td>TaskDueDate</td> <td>Used in the request body</td> <td>The task due date.</td> </tr> <tr> <td>TaskEstimatedHours</td> <td>Used in the request body</td> <td>The task's estimated completion time in hours.</td> </tr> <tr> <td>FollowerUserNamesCsv</td> <td>Used in the request body</td> <td>A comma-separated list of usernames&nbsp;of users to add as task followers.</td> </tr> </tbody> </table> <h3>Example Request</h3> <p><span style="font-size: 14pt">PUT</span> <a href="#">https://myintranet.communifire.com/api/tasks/734</a></p> <p>Body:</p> <pre><code class="json"> { "TaskName": "Prepare Q4 Financial Report", "TaskListID": 704, "SpaceID": 418, "TaskDescription": "Compile and analyze financial data for the Q4 <br> report, ensuring accuracy and completeness.", "AssignedToUserID": 573, "TaskStatusID": 3729, "TaskPriorityID": 2470, "TaskDueDate": "12/20/2024", "TaskEstimatedHours": 5, "FollowerUserNamesCsv": "robert,alice" }</code></pre> <h3>Example Response</h3> <p>Task JSON object is returned as ResponseData.</p> <pre><code class="json">{ "IsError": false, "ResponseMessage": "Task has been updated.", "ResponseData": { "TaskID": 734, "TaskName": "Prepare Q4 Financial Report", "TaskDescription": "Compile and analyze financial data for the Q4 <br> report, ensuring accuracy and completeness.", "IssueDescriptionUnprocessed": "Compile and analyze financial data <br> for the Q4 report, ensuring accuracy and completeness.", "TaskSortOrder": 1, "TaskListID": 704, "IsCompleted": false, "SpaceID": 418, "AssignedToUserID": 573, "TaskDueDate": "2024-12-20T00:00:00", "DateStringTaskEnd": "20/12/2024", "PastDue": false, "AssignedToUserDisplayName": "Bethany Kim", "TaskListName": "Finance List ", "TaskStatusTypeID": 0, "TaskStatusID": 3729, "TaskPriorityID": 2470, "DateCreated": "2024-12-11T23:04:02.4", "ReportedByUserID": 950, "DateUpdated": "2024-12-11T23:28:11.4954293Z", "FollowerUserNamesCsv": "602,987", "ReferencedEntityID": 0, "ReferencedEntityType": 0, "TaskEstimatedHours": 5.0 } }</code></pre> <p>&nbsp;</p> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>`,
      'rest-api-get-url-mapping': `<h1 id="rest-api-get-url-mapping">REST API: Get URL Mapping</h1>
<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title"><span>Get URL Mapping</span></h1> <h3 class="method-url"><span>GET /api/urlmapper</span></h3> <p><span>Get URL mapping details. Valid for Axero version 8.0 and above.</span></p> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP&nbsp;Method</span></div> <div class="span2"><span class="label label-warning type">GET</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc"><span>Product Version</span></span></div> <div class="span2"><span class="type">8.0 and above</span></div> </div> </div> </div> <table class="table table-striped" style="width: 100%"> <thead> <tr> <th class="name" style="width: 17.3716%">Required Parameters</th> <th class="use" style="width: 33.9879%">How to use</th> <th class="desc" style="width: 48.6405%">Description</th> </tr> </thead> <tbody> <tr> <td style="width: 17.3716%">API Key</td> <td style="width: 33.9879%"><span>Used in the request </span><span>URL:</span><br><span>&amp;token=x</span><br><span>OR</span><br><span>Set in header:</span><br><span>request.Headers.Add(Rest-Api-Key, x)</span></td> <td style="width: 48.6405%">Axero REST API key for the Axero portal you are making the call for.</td> </tr> <tr> <td style="width: 17.3716%">URLID</td> <td style="width: 33.9879%">Used in the request URL as a query parameter</td> <td style="width: 48.6405%">ID of URL Mapper entry you want to get.</td> </tr> </tbody> </table> <h3>Example Request&nbsp;</h3> <p><span style="font-size: 12pt">GET</span>&nbsp; <a href="https://your-community.com/api/urlmapper?urlID=55">https://your-community.com/api/urlmapper?urlID=55</a></p> <h3><span style="font-size: 18pt">Example Response</span></h3> <pre><code class="json">{ "IsError": false, "ResponseMessage": "", "ResponseData":{ "UrlID": 55, "OldUrl": "https://your-community.com/spaces/291/training-and-development/video/admin/instructional/9457/05-top-level-community-and-spaces", "NewUrl": "https://your-community.com/spaces/291/training-and-development/videos/admin/instructional/9457/05-top-level-community-and-spaces", "RedirectTypeID": 1, "IsActive": true, "DateAdded": "2022-09-21T20:34:39.8" } } </code></pre> <br> <p><span class="label label-important">Please Note</span>&nbsp;The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>`,
      'rest-api-add-url-mapping': `<h1 id="rest-api-add-url-mapping">REST API: Add URL Mapping</h1>
<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title"><span>Add URL Mapping</span></h1> <h3 class="method-url"><span>POST /api/urlmapper</span></h3> <p><span>Create a custom URL mapping to redirect one URL to another for improved navigation and accessibility. Valid for Axero version 8.0 and above.</span></p> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP&nbsp;Method</span></div> <div class="span2"><span class="label label-warning type">POST</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc"><span>Product Version</span></span></div> <div class="span2"><span class="type">8.0 and above</span></div> </div> </div> </div> <table class="table table-striped"> <thead> <tr> <th class="name">Required Parameters</th> <th class="use">How to use</th> <th class="desc">Description</th> </tr> </thead> <tbody> <tr> <td>API Key</td> <td><span>Used in the request </span><span>URL:</span><br><span>&amp;token=x</span><br><span>OR</span><br><span>Set in header:</span><br><span>request.Headers.Add(Rest-Api-Key, x)</span></td> <td>Axero REST API key for the Axero portal you are making the call for.</td> </tr> <tr> <td>fromURL</td> <td>Used in the request URL as a query parameter</td> <td>The URL you want to redirect from</td> </tr> <tr> <td>toURL</td> <td>Used in the request URL as a query parameter</td> <td>The URL you want to redirect to</td> </tr> </tbody> </table> <h3>Example Request&nbsp;</h3> <p><span style="font-size: 12pt">POST</span>&nbsp; <a href="https://your-community.com/api/urlmapper?fromURL=https://your-community.com/people/20164&amp;toURL=https://your-community.com/spaces/11165">https://your-community.com/api/urlmapper?fromURL=https://your-community.com/people/20164&amp;toURL=https://your-community.com/spaces/11165</a></p> <h3>Example Response</h3> <p>True/false&nbsp;is returned.</p> <pre><code class="json">true</code></pre> <p>&nbsp;</p> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>`,
      'rest-api-delete-url-mapping': `<h1 id="rest-api-delete-url-mapping">REST API: Delete URL Mapping</h1>
<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Delete URL Mapping</h1> <h3 class="method-url">DELETE /api/urlmapper/multiple</h3> <p>Remove one or multiple custom URL redirections from the system. Valid for Axero version 8.0 and above.</p> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP&nbsp;Method</span></div> <div class="span2"><span class="label label-warning type">DELETE</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Product Version</span></div> <div class="span2"><span class="type">8.0 and above</span></div> </div> </div> </div> <table class="table table-striped" style="background-color: rgba(255, 255, 255, 1); height: 180px"> <thead> <tr style="height: 40px"> <th class="name" style="height: 40px; width: 108.062px">Required Parameters</th> <th class="use" style="height: 40px; width: 223.297px">How to use</th> <th class="desc" style="height: 40px; width: 297.641px">Description</th> </tr> </thead> <tbody> <tr style="height: 120px"> <td style="height: 120px; width: 108.062px">API Key</td> <td style="height: 120px; width: 223.297px">Used in the request URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td style="height: 120px; width: 297.641px">Axero REST API key for the Axero portal you are making the call for.</td> </tr> <tr style="height: 20px"> <td style="height: 20px; width: 108.062px">toURL</td> <td style="height: 20px; width: 223.297px">Used in the request URL as a query parameter</td> <td style="height: 20px; width: 297.641px">The URL you want to redirect to</td> </tr> </tbody> </table> <table class="table table-striped" style="background-color: rgba(255, 255, 255, 1); width: 672px"> <thead> <tr style="height: 40px"> <th class="name" style="height: 40px; width: 105.172px">Optional Parameters</th> <th class="use" style="height: 40px; width: 224.312px">How to use</th> <th class="desc" style="height: 40px; width: 299.516px">Description</th> </tr> </thead> <tbody> <tr style="height: 22px"> <td style="width: 105.172px; height: 22px">fromURL</td> <td style="width: 224.312px; height: 22px">Used in the request URL as a query parameter</td> <td style="width: 299.516px; height: 22px">The URL you want to redirect from</td> </tr> </tbody> </table> <p class="method-url"><strong>NOTE:</strong> If "fromURL" is provided, the API will delete all mappings pointing from that URL.</p> <h3>Example Request&nbsp;</h3> <p><span style="font-size: 12pt">DELETE</span>&nbsp; <a href="https://your-community.com/api/urlmapper?toURL=https://your-community.com/spaces/11165">https://your-community.com/api/urlmapper?toURL=https://your-community.com/spaces/11165</a>&nbsp;</p> <h3>Example Response</h3> <p>True/false&nbsp;is returned.</p> <pre><code class="json">true</code></pre> <p>&nbsp;</p> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> <p>See also: <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22482/url-mapper">URL&nbsp;Mapper</a></p> </div> </div>`,
      'rest-api-get-axero-copilot-conversation-records': `<h1 id="rest-api-get-axero-copilot-conversation-records">REST API: Get Axero Copilot Conversation Records</h1>
<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Get Axero Copilot Conversation Records</h1> <h3 class="method-url">GET /api/analytics/copilot/conversations</h3> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <p class="method-desc">Retrieves conversation records and interaction details from Axero Copilot analytics, including conversation IDs, user IDs, timestamps, durations, questions, responses, and references.</p> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP&nbsp;Method</span></div> <div class="span2"><span class="label label-success type">GET</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Product Version</span></div> <div class="span2"><span class="type">8.x&nbsp;</span></div> </div> </div> </div> <table class="table table-striped"> <thead> <tr> <th class="name">Required Parameters</th> <th class="use">How to use</th> <th class="desc">Description</th> </tr> </thead> <tbody> <tr> <td>API Key</td> <td><span> Used in the request </span>URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td>Axero REST API key for the Axero portal you are making the call for.</td> </tr> </tbody> </table> <table class="table table-striped" style="width: 100%; height: 147px"> <thead> <tr style="height: 49px"> <th class="name" style="width: 22.5076%">Optional Parameters</th> <th class="use" style="width: 39.4285%">How to use</th> <th class="desc" style="width: 38.064%">Description</th> </tr> </thead> <tbody> <tr style="height: 49px"> <td style="width: 22.5076%">authorID</td> <td style="width: 39.4285%">&amp;authorID=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">The ID of the author to retrieve interactions from.</td> </tr> <tr style="height: 49px"> <td style="width: 22.5076%">question</td> <td style="width: 39.4285%">&amp;question=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">A keyword to search for in the interaction question text.</td> </tr> <tr style="height: 49px"> <td style="width: 22.5076%">feedback</td> <td style="width: 39.4285%">&amp;feedback=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">Filters interactions based on feedback type: <ul> <li><code>none</code> – No feedback provided.</li> <li><code>any</code> – Any feedback, regardless of type.</li> <li><code>positive</code> – Only interactions with positive feedback.</li> <li><code>negative</code> – Only interactions with negative feedback.</li> </ul> <p>NOTE: If not specified, all results will be included.</p> </td> </tr> <tr style="height: 49px"> <td style="width: 22.5076%">fromDate</td> <td style="width: 39.4285%">&amp;fromDate=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">Specifies the start date and time for filtering interactions, using the ISO format (YYYY-MM-DDTHH:mm:ss).</td> </tr> <tr style="height: 49px"> <td style="width: 22.5076%">toDate</td> <td style="width: 39.4285%">&amp;toDate=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">Specifies the end date and time for filtering interactions, using the ISO format (YYYY-MM-DDTHH:mm:ss).</td> </tr> <tr style="height: 49px"> <td style="width: 22.5076%">continuationToken</td> <td style="width: 39.4285%">&amp;continuationToken=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">A pagination token for retrieving more results when additional data is available. If included in the response, use the token in the next request to fetch the next set of records.</td> </tr> </tbody> </table> <h3>Example Request</h3> <p class="method-url"><span style="font-size: 12pt">GET&nbsp;</span> <a href="#"> https://your-community.com/api/analytics/copilot/conversations?authorID=1245&amp;question=policies&amp;feedback=positive&amp;fromDate=2025-02-01T18:40:12.713115Z&amp;toDate=2025-02-03T18:40:12.713115Z </a></p> <h3>Example Response</h3> <pre><code class="json">{ "Duration": 0.0, "Interactions": [ { "Duration": 14.399983406066895, "DiscussionId": "d864415a-5b32-4f4a-93e6-a9b58e9e99c9", "InteractionId": "ed96cf9f-b17d-447f-a81a-ae526f4b1512", "Question": "What are the company policies?", "Response": "The company policies cover various aspects of employment and workplace conduct at Axero. Here is a summary of some key policies:\\n\\n1. **Company Handbook**: This document serves as a guide for the employer/employee relationship and contains general information and guidelines. It is not comprehensive and is subject to change. Employees should consult the Human Resource department for specific questions about benefits or policies.\\n\\n2. **Community and Charity**: This policy outlines restrictions for distributing materials and soliciting funds, donations, and signatures on company premises during working hours. It applies to all employees, external visitors, partners, and customers.\\n\\n3. **401(k) Plan Policy**: This policy provides a 401(k) Retirement Savings Plan to help employees accumulate financial resources for retirement. Eligibility, contributions, and terms of the plan are detailed within the policy.\\n\\n4. **Employee Training and Development Policy**: This policy refers to the company's learning and development programs and activities, encouraging continuous professional development for all permanent employees.\\n\\n5. **Smoke-Free Workplace**: Smoking is prohibited on all company premises to maintain a safe and healthy work environment for all employees. The policy applies to all areas of company buildings, company-sponsored off-site conferences and meetings, vehicles owned or leased by the company, visitors, contractors, consultants, and all employees.\\n\\nFor detailed information on each policy, employees should refer to the specific documents provided by the company. These policies may be modified or discontinued from time to time, and employees will be informed of any changes as they occur.\\n\\nFor more detailed information, you can access the documents through the following links:\\n- [Company Handbook](https://myintranet.com/spaces/252/human-resources/wiki/view/6708/company-handbook?language=en-US)\\n- [Community and Charity](https://myintranet.com/spaces/252/human-resources/wiki/view/8504/community-and-charity?language=en-US)\\n- [401(k) Plan Policy](https://myintranet.com/spaces/252/human-resources/wiki/view/8499/401-k-plan-policy?language=en-US)\\n- [Employee Training and Development Policy](https://myintranet.com/spaces/252/human-resources/wiki/view/8495/employee-training-and-development-policy?language=en-US)\\n- [Smoke-Free Workplace](https://myintranet.com/spaces/252/human-resources/wiki/view/6849/smoke-free-workplace?language=en-US)\\n\\nPlease note that these policies are specific to Acme Ltd. and may not apply to other organizations.", "Feedback": "helpful", "UserId": "1245", "UserName": "Alice Romero", "TimestampEnd": "1738671871.1736572", "TimestampStart": "1738671856.7736738", "DateEndISO": "2025-02-04T12:24:31.173657Z", "DateStartISO": "2025-02-04T12:24:16.773674Z", "DiscussionDateStartISO": "2025-02-04T12:24:16.773418Z", "References": [ { "ContentId": "6708", "EntityType": "9", "Relevance": "100", "SpaceId": "252", "Title": "Company Handbook", "URL": "https://myintranet.com/spaces/252/human-resources/wiki/view/6708/company-handbook?language=en-US" }, { "ContentId": "8504", "EntityType": "9", "Relevance": "100", "SpaceId": "252", "Title": "Community and Charity", "URL": "https://myintranet.com/spaces/252/human-resources/wiki/view/8504/community-and-charity?language=en-US" }, { "ContentId": "8499", "EntityType": "9", "Relevance": "100", "SpaceId": "252", "Title": "401(k) Plan Policy", "URL": "https://myintranet.com/spaces/252/human-resources/wiki/view/8499/401-k-plan-policy?language=en-US" }, { "ContentId": "8495", "EntityType": "9", "Relevance": "100", "SpaceId": "252", "Title": "Employee Training and Development Policy", "URL": "https://myintranet.com/spaces/252/human-resources/wiki/view/8495/employee-training-and-development-policy?language=en-US" }, { "ContentId": "6849", "EntityType": "9", "Relevance": "100", "SpaceId": "252", "Title": "Smoke-Free Workplace", "URL": "https://myintranet.com/spaces/252/human-resources/wiki/view/6849/smoke-free-workplace?language=en-US" } ], "UserFeedback": { "FeedbackIssues": [], "FeedbackText": "", "FeedbackType": "positive" } } ], "Temperature": 0.0, "ContinuationToken": "eyJ0b2RkYifX0=" } </code></pre> <br> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>`,
      'create-wiki-page': `<h1 id="create-wiki-page">Create Wiki Page</h1>
<p><img alt="Click create in the header" src="https://my.axerosolutions.com/attachment?file=Lh0XYPy5sq7ZhOAJTkMEzQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="822" width="1440"></p> <p><img alt="Click Wiki page" src="https://my.axerosolutions.com/attachment?file=wgxwDIiJCFAW6Cj2WB1V9A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="874" width="1440"></p> <p>In the modal that opens, select the space you want to post to.</p> <p><img alt="Select a space" src="https://my.axerosolutions.com/attachment?file=qQOjFbzk7IqampuMSzms6A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="692" width="1228"></p> <p><img alt="Fill in required details" src="https://my.axerosolutions.com/attachment?file=HuFfsk71M5evR%2FgMltfmvg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1324" width="1440"></p> <p>Fill in required sections:</p> <ul> <li><strong>Page name</strong>: The&nbsp;title of the wiki.</li> <li><strong>Summary</strong>: A summary that will appear beside the wiki in the&nbsp;{{mention:21539:9}}&nbsp;.</li> <li><strong>Page content</strong>: Wiki&nbsp;main body.</li> <li><strong>Parent topic</strong>: The page your page will be sorted under. As you type in this section, suggestions of existing pages will appear in a dropdown menu.</li> </ul> <p>Fill in optional sections:</p> <ul> <li><strong>Upload a featured image</strong>:&nbsp;An image that will appear beside the wiki in the Activity Stream. You can upload an image from your computer, your albums, your clipboard, or your webcam.&nbsp;Suggested aspect ratio: 4:3</li> <li><strong>Add tags</strong>: Keywords that can help others find your wiki. Use spaces to separate tags. Use an underscore or dash to combine separate words into one tag (e.g., Vacation-Policy). Click <em class="icon-plus-sign-alt"></em> to see Popular Tags&nbsp;and Tag Groups.</li> <li><strong>Meta information&nbsp;- Meta title, Meta description</strong>:&nbsp;Information to help users find the wiki.</li> <li><strong>Author</strong>:&nbsp;Set an author to&nbsp;{{mention:34150:9}}.</li> <li><strong>Attach files</strong>:&nbsp;Add files relevant to the wiki. Drag and drop files into the area, or click Select Files.</li> <li><strong>Publication Date:&nbsp;</strong>Set the wiki&nbsp;to publish at a certain time and date.</li> <li><strong>Expiration date:</strong> Set the wiki to expire at a certain time and date.</li> </ul> <p>Options:</p> <ul> <li><strong>Featured</strong>:&nbsp;Make the wiki&nbsp;a featured post in the space.</li> <li><strong>Required reading</strong>: Require users to mark the wiki as read.</li> <li><strong>Allow comments</strong>: Allow users to comment on the wiki.</li> <li><strong>Allow non-members comments</strong>: Allow users who are not logged in to comment.</li> <li><strong>Allow likes</strong>: Allow users to like the wiki and comments on the wiki.</li> <li><strong>Allow ratings</strong>: Allow users to rate the wiki and comments on the wiki.</li> </ul> <p>To create collapsible&nbsp;content in your wiki, open Tools &gt; Source Code in the editor and insert the HTML code below.&nbsp;<span class="ax-role-site-administrator">(For security reasons, we filter out some HTML elements by default. You will need to add the attributes <code>data-toggle</code> and <code>data-target</code> to&nbsp;HTMLSanitizeAllowedAttributes&nbsp;in&nbsp;{{mention:334:9}}&nbsp;&gt;&nbsp;{{mention:335:9}}&nbsp;&gt;&nbsp;{{mention:22317:9}}&nbsp;.)</span></p> <pre><code class="html">&lt;a data-toggle="collapse" data-target="#demo"&gt;Click this to open/close&lt;/a&gt; &lt;div id="demo" class="collapse in"&gt; Your collapsed content should go in here. &lt;/div&gt;</code></pre> <p><img alt="Save and Publish" src="https://my.axerosolutions.com/attachment?file=S2SSOQqmIbcB215pV1VFCw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="923" width="1440"></p> <p>Once you've finished creating your wiki page, select one of the following options:</p> <ul> <li>Save and Publish</li> <li>Cancel</li> <li>{{mention:23816:9}}: Save&nbsp;the wiki page for later. You can find your drafts in&nbsp;{{mention:21494:9}}&nbsp;&gt;&nbsp;{{mention:21528:9}}&nbsp;</li> </ul> <h2><img alt="Activity Stream &gt; My Content" src="https://my.axerosolutions.com/attachment?file=yt67%2BYvbtzjGPsEYcJiQ%2Bw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1148" width="1440"></h2> <h2><img alt="My Content &gt; Wikis" src="https://my.axerosolutions.com/attachment?file=63r2Dk7UydK9drAK2cj4Xg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="837" width="1440"></h2> <h2>Alternative Way</h2> <p><img alt="Click wiki in the space menu" src="https://my.axerosolutions.com/attachment?file=kvgMhQxkJdVydvbdaMfYHQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="970" width="1440"></p> <p><img alt="Click Add Wiki page" src="https://my.axerosolutions.com/attachment?file=1B3NjawM3vN8jcYMhs5Qpg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="744" width="1440"></p> <h2>Related</h2> <p>{{mention:38473:9}}&nbsp;</p> <p>You've been given a task to share information with the entire organization, and you want to quickly and easily share the new exciting information with everyone by posting it on your homepage. In this guide, you'll learn how to make important information visible on your homepage.</p> <p>{{mention:26880:9}}&nbsp;</p> <p>Generate a table of contents based on the headings in your content. The table of contents will display the main headings in your content. People can quickly jump right to specific sections in the content using the table of contents.</p> <p>{{mention:26842:9}}&nbsp;</p> <p>You can create document templates to be used in the&nbsp;rich text editor. This allows your site administrator or space administrators to create HTML&nbsp;templates that&nbsp;users can select from a dropdown and insert into the editor&nbsp;textarea. If you create a lot of documents using a uniform layout, this saves you time and effort by creating a standard HTML layout for your template.</p>`,
      webhooks: `<h1 id="webhooks">Webhooks</h1>
<div class="mce-toc documentation-callout"> <h2><strong>Table of Contents</strong></h2> <ul> <li><a href="#webhooks-overview">Webhooks Overview</a></li> <li><a href="#managing-webhooks">Managing Webhooks</a> <ul> <li><a href="#creating-webhook">Creating and Editing Webhook</a></li> <li><a href="#testing-webhook">Enabling and Testing Webhook</a></li> <li><a href="#monitoring-webhook-activity">Monitoring Webhook Activity</a></li> <li><a href="#filtering-webhooks">Filtering Webhooks</a></li> <li><a href="#webhook-logs">Webhook Logs</a></li> <li><a href="#deleting-webhook">Deleting Webhook</a></li> </ul> </li> </ul> </div> <h2 id="webhooks-overview">Webhooks Overview</h2> <p>Webhooks provide a real-time communication mechanism between your platform and external applications. Instead of relying on periodic API polling, webhooks allow automated HTTP requests to be sent to a specified URL when specific events occur. This significantly improves efficiency, reduces API overhead, and enables seamless integration with third-party services.</p> <p>In Axero, you can configure webhooks to automate actions based on content events, triggered when content is published, updated, expired, or deleted.&nbsp;</p> <h2 id="managing-webhooks">Managing Webhooks</h2> <div class="documentation-callout"> <p><strong>Note</strong>: To enable Webhooks functionality, set the <em>WebhookEnabled </em>system property to true.</p> </div> <p>To manage webhooks, navigate to <strong>Control Panel</strong> &gt; <strong>System</strong> &gt; <strong>Integrations</strong> &gt; <strong>Webhooks.</strong></p> <p><strong><img alt="Control Panel &gt; System &gt; Integrations &gt; Webhooks" src="https://my.axerosolutions.com/attachment?file=gHeVkkV0YbUPvUcHiCngwA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="523" width="1440"></strong></p> <p><img alt="Webhook page" src="https://my.axerosolutions.com/attachment?file=V%2BBql7yeVr%2B8iWB1hZtAyA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="544" width="1440"></p> <h3 id="creating-webhook">Creating and Editing Webhook</h3> <p>To create a new webhook:</p> <ol> <li>Navigate to <strong>Control Panel &gt; System &gt; Integrations &gt; Webhooks</strong>.<br><br> <p><img alt="Add Webhook" src="https://my.axerosolutions.com/attachment?file=QsKRKLO5zdyoOPd04bTZzQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="443" width="1440"></p> </li> <li>Click <strong>+ Add Webhook</strong> button to initiate configuration.</li> <li><strong>Add Basic Webhook Information:</strong> <ul> <li><strong>Webhook Name:</strong> Enter a name to easily identify the webhook.</li> <li><strong>Description:</strong> Provide a brief description of the webhook’s purpose (optional).<br><br> <p><img alt="Webhook Details" src="https://my.axerosolutions.com/attachment?file=nVacB%2BDmgE9eVgw%2FcjNj0g%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1102" width="1312"></p> </li> </ul> </li> <li><strong>Set Integration Settings</strong> <ul> <li><strong>Target URL:</strong> Specify the external endpoint that will receive webhook data. Ensure the URL starts with <code>https://</code>.</li> <li><strong>Headers:</strong> Add an API key for authentication.</li> <li><strong>HTTP Method:</strong> Webhooks send data using a <strong>POST</strong> request.</li> </ul> </li> <li><strong>Set Event Configurations</strong> <ul> <li><strong>Space:</strong> Select whether the webhook applies to all spaces or a specific one.</li> <li><strong>Event Type:</strong> Select Content.</li> <li><strong>Trigger On:</strong> Defines when the webhook should activate: <ul> <li><strong>Content Published:</strong> Triggered when content is published or scheduled.</li> <li><strong>Content Updated:</strong> Triggered on any edit (excluding engagement updates).</li> <li><strong>Content Expired:</strong> Triggered when content reaches expiration.</li> <li><strong>Content Deleted:</strong> Triggered when content is permanently removed.</li> </ul> <p><img alt="Webhook &gt; Event configuration" src="https://my.axerosolutions.com/attachment?file=%2FHclrBaLutDFryD5GVAZKg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1384" width="1300"></p> </li> <li><strong>Content Types:</strong> Choose one or more applicable content types, or <em>Select All</em> to include all content types.</li> <li><strong>Include Fields:</strong> Select which content fields should be included in the payload, or <em>Select All</em> to include all fields. <p><img alt="Webhooks &gt; Include Fields" src="https://my.axerosolutions.com/attachment?file=MEgsWhjscKv6ZM%2FvpZCWbA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1306" width="1298"></p> </li> </ul> </li> <li><strong>Webhook Payload Preview</strong> <p>Before finalizing a webhook, administrators can preview an example of the data payload sent when an event occurs.</p> <p><img alt="Webhook Payload Preview" src="https://my.axerosolutions.com/attachment?file=4EGxILSfiCbdKkAy8vqpEw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1596" width="1282"></p> </li> <li><strong>Saving the Webhook</strong> <p>Once you have completed the configuration steps, click <strong>Save as Draft</strong>. The webhook will be added to the list in a disabled state. You can enable it and test it later from the configuration page.</p> <p><img alt="Webhook &gt; Save as Draft" src="https://my.axerosolutions.com/attachment?file=szRkGC0Qw%2FlB9Pn8ptlfPA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="893" width="1440"></p> </li> </ol> <h4>Volume Management&nbsp;</h4> <p>You can track how often the selected&nbsp;webhook has been activated over the last&nbsp;<strong>7 days</strong>&nbsp;and&nbsp;<strong>24 hours</strong>. To view detailed trigger volume details, click the&nbsp;<strong>Edit</strong>&nbsp;button and navigate to&nbsp;<strong>Volume Management</strong>.</p> <p>The Volume Management panel appears when first creating a webhook so you can anticipate the expected volume and frequency of triggers, helping prevent overload on the site or the external endpoint. It is based on historical data for the selected webhook settings and updates dynamically if you change Spaces, Triggers, or Content Types.</p> <p><img alt="Edit Webhook &gt; Volume Management" src="https://my.axerosolutions.com/attachment?file=QyzKkoQ1LnoSikX93PVFJg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1538" width="1326"></p> <h4>Editing Webhook</h4> <p>To edit an existing webhook, navigate to the <strong>Webhooks</strong> page and click <strong>Edit</strong> in the <strong>Actions</strong> column next to the webhook you want to modify.</p> <p>The configuration screen will open with the same fields and options used when creating a&nbsp;webhook.</p> <p>You can change the name, URL, headers, event triggers, or any other settings as needed.</p> <h3 id="testing-webhook">Enabling and Testing&nbsp;Webhook</h3> <p>A newly created webhook is disabled by default.&nbsp;You can test it in this state by clicking the <strong>Test</strong> button in the <strong>Actions</strong> column. This sends a sample request to the target URL, simulating an actual event, and is followed by a confirmation message. Test actions are not recorded in the&nbsp;webhook logs.&nbsp;</p> <p><img alt="Test Webhook" src="https://my.axerosolutions.com/attachment?file=6XGvDFRXB80iwDyJVs3eEA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="487" width="1440"></p> <p>After testing, toggle the webhook to <strong>enabled</strong>. Once enabled, it automatically triggers when the specified event occurs.&nbsp;</p> <p><img alt="Enable Webhook" src="https://my.axerosolutions.com/attachment?file=IAAMVFXUhUVqACocsW8%2Fnw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="484" width="1440"></p> <p><span style="font-size: 12pt; font-family: Arial, sans-serif; color: rgba(0, 0, 0, 1)">Webhook logs are automatically archived based on global retention policies configured in <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/20987/log-archive-settings">Log Archive Settings</a></strong>, ensuring past events remain accessible as needed.</span></p> <h3 id="monitoring-webhook-activity">Monitoring Webhook Activity</h3> <p>The <strong>Last Triggered</strong> column provides insight into webhook activity. Each entry includes:</p> <ul> <li>A&nbsp;timestamp indicating when the webhook last fired.</li> <li>The status of the last trigger (Success, Failed, or Pending).</li> <li>The event that triggered the webhook (e.g., content.published, content.updated).&nbsp;</li> </ul> <p><img alt="Webhook &gt; Test Results" src="https://my.axerosolutions.com/attachment?file=ruWW9Y342GhHag7xwmWdWg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="562" width="1440"></p> <h3 id="filtering-webhooks">Filtering Webhooks</h3> <p><img alt="Filtering Webhooks" src="https://my.axerosolutions.com/attachment?file=%2F%2FYwrTGv4tifyUyrIChbxg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="499" width="1440"></p> <p>The&nbsp;Webhook Configurations page displays a list of existing webhooks along with filtering options to narrow them down by:</p> <ul> <li> <p><strong>Event Type: </strong>Specifies whether the webhook is triggered by all system events or only content-related events.</p> </li> <li> <p><strong>Triggered On: </strong>Allows filtering based on when the webhook was triggered, with options such as Published, Updated, Expired, or Deleted.</p> </li> <li> <p><strong>Space: </strong>Defines whether the webhook applies to all spaces or a specific one.</p> </li> <li> <p><strong>Content Type: </strong>Narrows down webhooks based on the type of content they handle, such as articles, albums, or videos.</p> </li> </ul> <h3 id="webhook-logs">Webhook Logs</h3> <p><img alt="Webhook Logs" src="https://my.axerosolutions.com/attachment?file=LCIm3a4YRpoRhl%2FHzSQ0bQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="407" width="1440"></p> <p>The&nbsp;<strong>Logs</strong> page provides a complete history of all events where a&nbsp;webhook was triggered, whether it completed successfully or failed. This allows administrators to monitor webhook activity, investigate errors, and retry failed attempts.</p> <p>Each log entry displays the event timestamp, webhook name, event type, status (Success or Error), and the source of the trigger. You can view additional information about any entry by selecting <strong>View details</strong> in the Actions column.</p> <h4>Filtering Logs</h4> <p><img alt="Filtering Logs" src="https://my.axerosolutions.com/attachment?file=x6f5TyI5Es5vkyYWZpCiPg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="411" width="1440"></p> <p>The Logs page includes filtering options to help narrow the list of displayed entries based on selected criteria:</p> <ul> <li> <p><strong>Date Range: </strong>Specifies a start and end date to display logs from a specific time period.</p> </li> <li> <p><strong>Status: </strong>Filters results by All Statuses, Success, or Error.</p> </li> </ul> <p>After selecting your criteria, click <strong>Go</strong> to refresh the list, and update the results.</p> <h4>Log Details</h4> <p>Each log entry includes an option to view detailed information about the webhook event. You can access these details from the <strong>Actions</strong> column by selecting <strong>View details</strong>. This helps administrators understand what was sent, how the receiving system responded, and who triggered the event. <br>The original payload and the response from the destination system are included to support troubleshooting and confirm a successful&nbsp;webhook execution. If the&nbsp;webhook failed, the request can be retried.&nbsp;</p> <p><img alt="Log Details" src="https://my.axerosolutions.com/attachment?file=P%2BI8PCLMEuLWZSJu3gElcg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="864" width="1422"></p> <h3 id="deleting-webhook">Deleting Webhook</h3> <p>To delete a webhook, click the <strong>Delete</strong> button in the Actions column.</p> <p><img alt="Delete Webhook" src="https://my.axerosolutions.com/attachment?file=FRStRUwVwxc74OBVtFzDyQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="489" width="1440"></p> <p><img alt="Delete &gt; a confirmation dialog " src="https://my.axerosolutions.com/attachment?file=5JpSwi89Oea91a7n6sPd2Q%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="494" width="1440"></p> <p>&nbsp;A confirmation dialog will appear, requiring you to choose between:</p> <ol> <ul> <li>Keeping the Webhook Name in User Activity and Webhook Logs – The name remains visible in logs even after deletion.</li> <li>Redacting the Webhook Name in User Activity and Webhook Logs – The name is removed from logs for privacy.</li> </ul> </ol> <p>Deletion is permanent and cannot be undone. Click <strong>Delete</strong> to confirm or <strong>Cancel</strong> to return without making changes.<span style="color: rgba(224, 62, 45, 1)"></span></p>`,
      'saml-20-sso': `<h1 id="saml-20-sso">SAML 2.0 SSO</h1>
<p>This guide provides step-by-step instructions for integrating SAML 2.0 Single Sign-On (SSO) with your Axero platform using third-party identity providers such as Duo, Shibboleth, IBM Verify, CyberArk, and others. By following this guide, you will enable secure, seamless SSO for your organization using any standards-compliant SAML 2.0 provider.</p> <hr> <div class="mce-toc"> <h2>Overview</h2> <ul> <li><a href="#prerequisites">Prerequisites</a></li> <li><a href="#user-login-experience">User Login Experience</a></li> <li><a href="#provider-configuration">Provider Configuration</a></li> <li><a href="#axero-configuration">Axero Configuration</a></li> <li><a href="#testing">Testing Your Configuration</a></li> <li><a href="#user-management">User Management</a></li> <li><a href="#data-mapping">User Data Mapping</a></li> <li><a href="#advanced-options">Advanced Options</a></li> <li><a href="#troubleshooting">Troubleshooting</a></li> <li><a href="#best-practices">Best Practices</a></li> <li><a href="#support">Getting Support</a></li> </ul> </div> <hr> <h2 id="prerequisites">Prerequisites</h2> <ul> <li><strong>Axero intranet site</strong> with administrator access</li> <li><strong>SAML 2.0 identity provider</strong> (such as Duo, Shibboleth, IBM Verify, CyberArk, etc.) with administrative access</li> <li><strong>SSL certificate</strong> for your Axero domain (HTTPS is required for SAML security)</li> <li><strong>Network connectivity</strong> between Axero and your SAML provider</li> <li><strong>Understanding of your organization's user directory structure</strong> and attribute naming conventions</li> </ul> <hr> <h2 id="user-login-experience">User Login Experience</h2> <ul> <li><strong>Web:</strong> Users are redirected to your SAML provider for authentication. If the <strong>EnableAutoLoginViaSaml</strong> system property is set to <code>false</code>, users can choose between SAML SSO and Axero credentials on the login page.</li> <li><strong>Mobile App:</strong> Users are redirected to the SAML provider and authenticate via SSO, following the same process as the web platform.</li> <li><strong>Session Management:</strong> Session duration is controlled by Axero's authentication settings. When the <code>MakePermanentCookieForThirdPartyLogin</code> system property is set to <code>false</code>, users are logged out when the browser session ends.</li> </ul> <hr> <h2 id="provider-configuration">Provider Configuration</h2> <p><strong>Important:</strong> The exact steps and terminology vary significantly by provider. Consult your provider's specific SAML documentation for detailed instructions. The following are general configuration steps:</p> <ol> <li>Create a new SAML application/integration for Axero in your provider's admin console.</li> <li>Set the <strong>Assertion Consumer Service (ACS) URL</strong> to:<br><code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">https://youraxerosite.com/SAML/AssertionConsumerService.aspx</code><br><em>Replace "youraxerosite.com" with your actual Axero domain.</em></li> <li>Set the <strong>Entity ID</strong> (also called <strong>Audience URI</strong> or <strong>SP Entity ID</strong>) to your Axero site URL or a unique identifier of your choice.</li> <li>Configure the <strong>NameID format</strong>. Common options include: <ul> <li><strong>Email Address:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress</code></li> <li><strong>Persistent:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">urn:oasis:names:tc:SAML:2.0:nameid-format:persistent</code></li> <li><strong>Unspecified:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified</code></li> </ul> </li> <li>Configure attribute/claim mappings. The specific attribute names vary by provider, but commonly include: <ul> <li><strong>NameID:</strong> User's unique identifier (typically email or username)</li> <li><strong>Email:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress</code> or <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">email</code></li> <li><strong>First Name:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname</code> or <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">givenname</code></li> <li><strong>Last Name:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname</code> or <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">surname</code></li> <li>Additional attributes as needed for your organization</li> </ul> </li> <li>Enable <strong>SAML Response Signing</strong> for security (strongly recommended).</li> <li>Download the IdP metadata XML file or note the SSO/Logout URLs and signing certificate for use in Axero configuration.</li> </ol> <hr> <h2 id="axero-configuration">Axero Configuration</h2> <ol> <li>In Axero, go to <strong>Control Panel &gt; System &gt; Single Sign On</strong>.</li> <li>Select <strong>SAML</strong> as the SSO type.</li> <li>Click <strong>Save</strong>.</li> <li>Enter the following information from your SAML provider: <ul> <li><strong>Partner Identity Provider URL:</strong> The Entity ID from your IdP</li> <li><strong>Single Sign On Service URL:</strong> The SSO endpoint URL from your IdP</li> <li><strong>Relying Party Trust Identifier:</strong> Your Axero site URL or the Entity ID you configured in your provider</li> <li><strong>Single Logout Service URL:</strong> The SLO endpoint URL from your IdP (optional — only if Single Logout is supported by your provider)</li> <li><strong>Partner Identity Certificate (CER):</strong> Upload the public signing certificate from your IdP</li> </ul> </li> <li>Click <strong>Update</strong> to save your settings.</li> <li>Click <strong>Data Mapping</strong> to configure user attribute mapping (see User Data Mapping section below).</li> </ol> <hr> <h2 id="testing">Testing Your Configuration</h2> <p><strong>Important:</strong> Always test your SAML configuration thoroughly before deploying to all users.</p> <ol> <li><strong>Test with a single user account:</strong> <ul> <li>Create or use an existing test user in your SAML provider</li> <li>Ensure the test user has all required attributes populated</li> <li>Attempt to log into Axero using SSO</li> </ul> </li> <li><strong>Verify the login flow:</strong> <ul> <li>Navigate to your Axero site</li> <li>You should be redirected to your SAML provider for authentication</li> <li>After successful authentication, you should be redirected back to Axero</li> <li>Verify that user data is properly mapped and displayed in Axero</li> </ul> </li> <li><strong>Test logout functionality:</strong> <ul> <li>Log out from Axero</li> <li>If Single Logout (SLO) is configured, verify you are logged out of both Axero and your SAML provider</li> </ul> </li> <li><strong>Test error scenarios:</strong> <ul> <li>Test with a disabled user account</li> <li>Test with a user missing required attributes</li> <li>Verify appropriate error messages are displayed</li> </ul> </li> </ol> <hr> <h2 id="user-management">User Management</h2> <ul> <li><strong>Automatic User Creation:</strong> By default, Axero creates a user account the first time someone logs in via SAML SSO (if <code>SAMLAutoUserCreation</code> is enabled).</li> <li><strong>Pre-Provisioning Users:</strong> You can add users in advance using: <ul> <li>Bulk Import (usernames must match SAML NameID values)</li> <li>Control Panel &gt; People &gt; Manage People &gt; Add User</li> <li>REST API for automated provisioning</li> </ul> </li> <li><strong>Administrator Accounts:</strong> Ensure admin account usernames match SAML user identifiers before enabling SSO, or update permissions after SSO is configured.</li> <li><strong>User Deactivation:</strong> Disabling a user in your SAML provider prevents new SSO logins, but the Axero account remains active. You must manually disable or delete users in Axero for complete access removal.</li> </ul> <hr> <h2 id="data-mapping">User Data Mapping</h2> <p>To import user profile data from your SAML provider to Axero, you need to map SAML attributes to Axero properties:</p> <ol> <li>Go to <strong>Control Panel &gt; System &gt; Single Sign On &gt; Data Mapping</strong>.</li> <li>Select <strong>SAML</strong> as the type.</li> <li>Add attribute mappings. The <strong>Property Name</strong> must match the exact attribute name sent by your SAML provider in the SAML assertion.</li> </ol> <table cellpadding="4" cellspacing="0"> <tbody> <tr> <th>SAML Attribute</th> <th>Axero Field</th> <th>Description</th> </tr> <tr> <td>NameID</td> <td>Username</td> <td>User's unique identifier (automatically mapped)</td> </tr> <tr> <td style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress</td> <td>Email</td> <td>Email address</td> </tr> <tr> <td style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname</td> <td>First Name</td> <td>User's first name</td> </tr> <tr> <td style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname</td> <td>Last Name</td> <td>User's last name</td> </tr> <tr> <td>department</td> <td>Department</td> <td>User's department (example custom attribute)</td> </tr> <tr> <td>title</td> <td>Job title</td> <td>User's job title (example custom attribute)</td> </tr> </tbody> </table> <p><strong>Important Notes:</strong></p> <ul> <li>Attribute names are case-sensitive and must match exactly as sent by your SAML provider</li> <li>Standard SAML claim URIs (the long URLs shown above) are recommended for better interoperability</li> <li>To verify which attributes your provider sends, use browser developer tools to inspect the SAML response during login, or contact your SAML provider administrator for the exact attribute names</li> <li>Custom attributes may use simple names (like "department") depending on your provider's configuration</li> </ul> <hr> <h2 id="advanced-options">Advanced Options</h2> <p>These options are configured in Axero's System Properties:</p> <ul> <li><strong>Disable SSO Auto Login:</strong> Set <code>EnableAutoLoginViaSaml</code> to <code>false</code> to allow users to choose between Axero and SSO login on the login page</li> <li><strong>Automatic User Creation:</strong> <code>SAMLAutoUserCreation</code> controls whether new users are created automatically on SSO login</li> <li><strong>Email Matching:</strong> <code>SAMLUserEmailMatch</code> enforces email address matching for SSO logins</li> <li><strong>Automatic User Updates:</strong> <code>SAMLAutoUserUpdate</code> controls whether user data is updated from SSO on each login</li> <li><strong>Session Persistence:</strong> <code>MakePermanentCookieForThirdPartyLogin</code> determines whether SSO sessions persist after browser closure</li> </ul> <hr> <h2 id="troubleshooting">Troubleshooting</h2> <p>Common issues and solutions when implementing SAML 2.0 SSO with Axero:</p> <h3>Authentication Issues</h3> <table style="vertical-align: top"> <thead> <tr> <th style="vertical-align: top; width: 25% !important">Issue</th> <th style="vertical-align: top; width: 25% !important">Possible Causes</th> <th style="vertical-align: top; width: 50% !important">Solution</th> </tr> </thead> <tbody> <tr> <td style="vertical-align: top">Login fails or users are not redirected to SAML provider</td> <td style="vertical-align: top">SAML SSO not enabled, incorrect provider URLs, misconfigured application settings</td> <td style="vertical-align: top">Verify that SAML SSO is enabled in Axero (<strong>Control Panel &gt; System &gt; Single Sign On</strong>). Check that all provider URLs (Entity ID, SSO URL, ACS URL) are entered correctly in both systems. Verify the SAML application is active and properly configured in your provider.</td> </tr> <tr> <td style="vertical-align: top">SSO loop or repeated redirects</td> <td style="vertical-align: top">Mismatched URLs between SAML provider and Axero, browser cache issues, incorrect ACS URL configuration</td> <td style="vertical-align: top">Ensure the Assertion Consumer Service URL (<code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">https://yourdomain/SAML/AssertionConsumerService.aspx</code>) matches exactly between your SAML provider and Axero configuration. Clear browser cookies and cache. Verify that the Entity ID/Relying Party Trust identifier is identical in both systems.</td> </tr> <tr> <td style="vertical-align: top">"Invalid SAML response" or assertion errors</td> <td style="vertical-align: top">Unsigned SAML response, certificate mismatch, incorrect Entity ID configuration</td> <td style="vertical-align: top">Ensure the SAML response from your provider is properly signed and the certificate in Axero matches the signing certificate from your provider. Verify that the Entity ID matches exactly between systems.</td> </tr> <tr> <td style="vertical-align: top">"There is no SAML configuration" or similar error</td> <td style="vertical-align: top">SAML not enabled, missing required configuration fields, configuration not saved properly</td> <td style="vertical-align: top">Verify that SAML is enabled in Axero and all required fields are completed. Ensure the SAML application is properly configured and active in your provider. Configuration changes may require a few minutes to take effect.</td> </tr> <tr> <td style="vertical-align: top">Browser-specific login issues</td> <td style="vertical-align: top">Browser cache, disabled cookies, browser extensions blocking redirects, popup blockers</td> <td style="vertical-align: top">Clear browser cache and cookies, or test with a different browser. Ensure cookies and third-party cookies are enabled. Disable browser extensions that may interfere with redirects. Check popup blocker settings.</td> </tr> </tbody> </table> <h3>Certificate and Security Issues</h3> <table style="vertical-align: top"> <thead> <tr> <th style="vertical-align: top; width: 25% !important">Issue</th> <th style="vertical-align: top; width: 25% !important">Possible Causes</th> <th style="vertical-align: top; width: 50% !important">Solution</th> </tr> </thead> <tbody> <tr> <td style="vertical-align: top">Certificate errors or invalid certificate</td> <td style="vertical-align: top">Expired certificates, incorrect certificate format, certificate mismatch between provider and Axero</td> <td style="vertical-align: top">Ensure the uploaded certificate is the correct public signing certificate from your SAML provider. The certificate must be in .cer, .crt, or .pem format and must not be expired. When your provider rotates certificates, update the certificate in Axero immediately. Verify the certificate matches the one used to sign SAML responses.</td> </tr> </tbody> </table> <h3>User Data and Attribute Mapping Issues</h3> <table style="vertical-align: top"> <thead> <tr> <th style="vertical-align: top; width: 25% !important">Issue</th> <th style="vertical-align: top; width: 25% !important">Possible Causes</th> <th style="vertical-align: top; width: 50% !important">Solution</th> </tr> </thead> <tbody> <tr> <td style="vertical-align: top">Attribute mapping or user data not syncing</td> <td style="vertical-align: top">Missing attribute configuration in SAML provider, incorrect attribute names, case sensitivity issues, attributes not included in assertion</td> <td style="vertical-align: top">Verify that attribute names in your SAML provider configuration match those in Axero's Data Mapping exactly (case-sensitive). Ensure the SAML provider is configured to include these attributes in assertions. Test with a user who has all required attributes populated. Contact your SAML provider administrator to confirm the exact attribute names being sent.</td> </tr> <tr> <td style="vertical-align: top">Email or username changes not reflected</td> <td style="vertical-align: top">User data sync disabled, timing of updates, manual intervention required for usernames</td> <td style="vertical-align: top">Email changes in your SAML provider will update in Axero on the user's next login if <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">SAMLAutoUserUpdate</code> is enabled. Username changes typically require manual updates in Axero as they affect the user's unique identifier.</td> </tr> <tr> <td style="vertical-align: top">Role or group information not syncing</td> <td style="vertical-align: top">Missing group/role claims configuration, incorrect role attribute format, groups not mapped</td> <td style="vertical-align: top">Configure your SAML provider to send group/role information using standard SAML attributes (such as <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.microsoft.com/ws/2008/06/identity/claims/role</code>). Verify users are assigned to appropriate groups in your provider and that group names are correctly configured.</td> </tr> </tbody> </table> <h3>User Provisioning and Access Issues</h3> <table style="vertical-align: top"> <thead> <tr> <th style="vertical-align: top; width: 25% !important">Issue</th> <th style="vertical-align: top; width: 25% !important">Possible Causes</th> <th style="vertical-align: top; width: 50% !important">Solution</th> </tr> </thead> <tbody> <tr> <td style="vertical-align: top">New users not being created in Axero</td> <td style="vertical-align: top">User account disabled in SAML provider, <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">SAMLAutoUserCreation</code> disabled, username format mismatch</td> <td style="vertical-align: top">Ensure the user account is active in your SAML provider and that <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">SAMLAutoUserCreation</code> is enabled in Axero System Properties. Verify the NameID format matches expectations. Consider pre-provisioning users in Axero with matching usernames.</td> </tr> <tr> <td style="vertical-align: top">Disabled users can still access Axero</td> <td style="vertical-align: top">No automatic account synchronization between SAML provider and Axero</td> <td style="vertical-align: top">Disabling a user in your SAML provider prevents new SSO logins, but existing Axero accounts remain active. Manually disable or delete user accounts in Axero for complete access removal. Consider implementing automated user lifecycle management processes.</td> </tr> </tbody> </table> <hr> <h2 id="best-practices">Best Practices</h2> <h3>Security Best Practices</h3> <ul> <li><strong>Always use HTTPS:</strong> Ensure all endpoints use secure HTTPS connections with valid SSL certificates. SAML 2.0 requires secure transport for security assertions.</li> <li><strong>Enable SAML Response Signing:</strong> Configure your SAML provider to sign SAML responses and ensure Axero validates these signatures.</li> <li><strong>Implement least privilege access:</strong> Grant only necessary permissions to service accounts and users involved in SAML authentication.</li> <li><strong>Minimize attribute exposure:</strong> Only configure attribute mappings for data actually needed in Axero. Avoid transmitting sensitive or unnecessary personal information.</li> <li><strong>Regular access reviews:</strong> Periodically audit user access in both your SAML provider and Axero. Remove accounts for users who no longer require access.</li> <li><strong>Secure certificate management:</strong> Maintain secure backups of certificates and private keys. Use proper certificate storage practices and limit access to certificate files.</li> <li><strong>Monitor authentication logs:</strong> Review SAML provider logs and Axero system logs regularly to identify failed authentication attempts or configuration issues.</li> </ul> <h3>Configuration Management</h3> <ul> <li><strong>Document your configuration:</strong> Maintain detailed documentation of all SSO-related settings, including: <ul> <li>SAML provider application configuration and settings</li> <li>Attribute mappings and claim configurations</li> <li>Axero SAML configuration parameters</li> <li>Certificate details and renewal schedules</li> <li>System property values and their purposes</li> </ul> </li> <li><strong>Version control configuration changes:</strong> Keep backups of working configurations before making changes to enable quick rollback.</li> <li><strong>Test in staging first:</strong> Always validate SSO configuration in a non-production environment before deploying to production. Test with multiple user accounts having different attribute combinations.</li> <li><strong>Plan rollback procedures:</strong> Document processes to quickly revert SAML configuration changes if issues arise.</li> </ul> <h3>Operational Best Practices</h3> <ul> <li><strong>Certificate lifecycle management:</strong> Monitor certificate expiration dates and plan renewals well in advance. Update certificates in Axero immediately when your SAML provider rotates them.</li> <li><strong>Plan for user lifecycle management:</strong> Establish processes for: <ul> <li>Onboarding new users with appropriate attributes in your SAML provider</li> <li>Updating user information when roles or attributes change</li> <li>Deactivating users when they leave the organization</li> </ul> </li> <li><strong>Maintain emergency access:</strong> Keep alternative authentication methods or emergency administrator accounts that don't depend on SAML authentication.</li> <li><strong>Communicate changes proactively:</strong> Notify users in advance of SSO-related changes, maintenance windows, or expected downtime.</li> </ul> <h3>Performance and Reliability</h3> <ul> <li><strong>Monitor system performance:</strong> Track SAML authentication response times and identify potential bottlenecks.</li> <li><strong>Plan for high availability:</strong> Consider your SAML provider's availability and your organization's uptime requirements.</li> <li><strong>Implement health checks:</strong> Set up monitoring to detect SAML provider issues, certificate problems, or connectivity issues quickly.</li> <li><strong>Capacity planning:</strong> Monitor resource usage during peak authentication times and plan for user base growth.</li> </ul> <h3>User Experience</h3> <ul> <li><strong>Provide user training:</strong> Educate users about the SSO experience, including login and logout processes.</li> <li><strong>Clear error messaging:</strong> Ensure users understand what to do when SAML authentication fails and provide alternative contact methods.</li> <li><strong>Browser compatibility guidance:</strong> Provide instructions for configuring different browsers for optimal SAML SSO experience.</li> <li><strong>Support multiple platforms:</strong> Document the authentication experience across different devices and platforms (web, mobile app).</li> </ul> <hr> <h2 id="support">Getting Support</h2> <p>If you encounter issues or need assistance, please <a href="https://my.axerosolutions.com/spaces/77/communifire-support/cases/add-edit-case/0">submit a private case</a> to the Axero support team. When submitting a support case, include:</p> <ul> <li>Detailed description of the issue and steps to reproduce</li> <li>Troubleshooting steps you've already attempted</li> <li>Error messages and relevant log entries from both Axero and your SAML provider</li> <li>Browser and operating system information for affected users</li> <li>Screenshots of configuration settings (with sensitive information redacted)</li> <li>SAML response content (if available and with sensitive data removed)</li> </ul> <p>This information will help the support team provide faster and more accurate assistance.</p>`,
      journeys: `<h1 id="journeys">Journeys</h1>
<p>Journeys are structured and sequenced&nbsp;workflows that guide individuals through key stages of the employee lifecycle, including&nbsp;onboarding, role transitions, and skill development. They consist of steps that people must complete, like reading content, visiting links, completing forms, and meeting training requirements. These are combined with timed emails or notifications that are sent to both the employee and their supervisor.</p> <p>As employees progress through the requirements, they can also be added or removed from spaces and earn digital badges. In this way, administrators and people managers can guide individuals through transitions with information and pointers at their own pace, boosting site engagement and keeping participants on track without requiring manual follow-up.</p> <p>Unlike learning management systems (LMS), journeys do not include testing, certification, or grading. Instead, they provide a flexible way to deliver content and guide employees through structured experiences such as onboarding, performance reviews, leadership development, and compliance training (with the option to link out to LMS courses as one of the required steps).</p> <h3>Enable Journeys and Configure Menu Icons</h3> <p>To make Journeys accessible to users, enable Journeys in system settings and add the icon to the header and user account menu.&nbsp;</p> <h4>Enable Journeys in System Properties</h4> <ol> <li>Go to <strong>Control Panel &gt; System &gt; System Properties</strong>.</li> <li>Set the property <strong>JourneysEnabled</strong> to <strong>true</strong>.</li> </ol> <p><img alt="JourneysEnabled" src="https://my.axerosolutions.com/attachment?file=nYGiA%2FLpKuonQpZqGmj%2FHA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="266" width="1440"></p> <h4>Add the Journeys Icon to the Navigation Header</h4> <ol> <li>Go to <strong>Control Panel &gt; System &gt; Page Builder &gt; Header Tab</strong><span> &gt; </span><strong>Edit</strong>.<br><br> <p><img alt="Edit Header" src="https://my.axerosolutions.com/attachment?file=DXGeTeCHi3xXffNtBMNbKw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="811" width="1440"></p> </li> <li>Drag the <strong>Button</strong> widget and drop it under the <strong>Navigation</strong> section of the page layout. <br><br> <p><img alt="Add button" src="https://my.axerosolutions.com/attachment?file=WPXZdxoTp69KDUxsGvAKlw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="732" width="1440"></p> </li> <li>Configure the <strong>Journey Button</strong>. <br><br> <p><img alt="Navigation &gt; Journey button" src="https://my.axerosolutions.com/attachment?file=YgKZU%2FCir4mrw%2BxOa8BZ%2FA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="818" width="1440"></p> </li> <li><strong>Save</strong> the widget and <strong>publish</strong> the changes.</li> </ol> <p>The Journey icon will appear in the navigation header.&nbsp;</p> <p><img alt="Journeys icon in navigation menu" src="https://my.axerosolutions.com/attachment?file=drz7Pw1FKqWBL1JzVYjM5g%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="682" width="1440"></p> <h4>Add the Journey Icon to My Account</h4> <ol> <li>Go to <strong>Control Panel &gt; System &gt; Page Builder &gt; Header Tab</strong><span> &gt; </span><strong>Edit</strong>.</li> <li>Expand the <strong>Navigation</strong> widget, then open <strong>My Account</strong> and select <strong>Add Item</strong>. <br><br> <p><img alt="Add item" src="https://my.axerosolutions.com/attachment?file=oWDsJjiaI0x%2BnzIT%2BKrjZg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="818" width="1440"></p> </li> <li>Configure the Journey item.<br><br> <p><img alt="Edit Item " src="https://my.axerosolutions.com/attachment?file=3WLJ92XkcTiIHMmuU%2FqgVg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1058" width="1440"></p> </li> <li><strong>Save</strong> the widget configuration and <strong>publish</strong> the changes.</li> </ol> <p>When you click your profile or account menu, the Journey icon will be visible under <strong>My&nbsp;Apps and Tools</strong>.<img alt="My Account &gt; Apps and Tools &gt; Journey" src="https://my.axerosolutions.com/attachment?file=3DzfgWmgR44uXa7cUeUlAA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="781" width="1440"></p> <h3>Managing Journey Permissions</h3> <p>To manage who can create, edit, or delete journeys, go to <strong>Control Panel &gt; People &gt; User Section Permissions</strong>.</p> <p><strong>Note:</strong> Only users with the <strong>Manage Permissions</strong> setting enabled can modify journey access rights for other roles.<br><br><img alt="User Section Permissions" src="https://my.axerosolutions.com/attachment?file=I2TiJb5tIw9Kwtjt0EUvpA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="678" width="1440"></p> <h3>Accessing the Journeys Manager</h3> <p>Users with permission <span style="color: rgba(0, 0, 0, 1)">to create or manage journeys</span> can open the Journey Manager from their profile menu.</p> <p>To access the Journey Manager, click your <strong>profile image</strong> in the top navigation bar and select <strong>Journey</strong> under <strong>My Apps and Tools</strong>.</p> <p><img alt="Apps and Tools &gt; Journeys" src="https://my.axerosolutions.com/attachment?file=2ERH2OUQCewLg%2FTOxkbRCw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="927" width="1440"></p> <h3>Managing Journey Permissions</h3> <p>To manage who can create, edit, or delete journeys, go to <strong>Control Panel &gt; People &gt; User Section Permissions</strong>.</p> <p><strong>Note:</strong> Only users with the <strong>Manage Permissions</strong> setting enabled can modify journey access rights for other roles.<br><br><img alt="User Section Permissions" src="https://my.axerosolutions.com/attachment?file=rkXg6dzQ3ym8Ds18zsDxlA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="678" width="1440"></p> <h3>Accessing the Journeys Manager</h3> <p>Users with permission <span style="color: rgba(0, 0, 0, 1)">to create or manage journeys</span> can open the Journey Manager from their profile menu.</p> <p>To access the Journey Manager, click your <strong>profile image</strong> in the top navigation bar and select <strong>Journey</strong> under <strong>My Apps and Tools</strong>.</p> <p><img alt="Apps and Tools &gt; Journeys" src="https://my.axerosolutions.com/attachment?file=%2BOLNsfH9Kg9bpzVewAdzNg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="927" width="1440"></p> <h2 id="journeys-interface">Journeys Manager Interface</h2> <p>The Journeys Manager provides an overview of all existing journeys. Admins can search, sort, filter journeys, and view details such as status, <span style="color: rgba(0, 0, 0, 1)">creator</span>, and participant count.&nbsp;</p> <h3><img alt="Journey Manager" src="https://my.axerosolutions.com/attachment?file=Tlmf%2BH2wTiI7Q9bvxvOZZw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="566" width="1440"></h3> <h3 id="filtering-journeys">Filtering Journeys</h3> <p>The filters above the table allow you to narrow the list of journeys. You can filter by status, date range, creator, or search by name.</p> <ul> <li> <p><strong>Status:</strong> Use the dropdown menu to filter journeys by their current state. Available options include Draft, Active, Paused, and Archived.</p> </li> <li> <p><strong>Date Range:</strong> Select a <strong>Start Date</strong> and <strong>End Date</strong> to view journeys created within a specific time period.</p> </li> <li> <p><strong>Created by:</strong> Enter a user’s name in the <strong>Created by</strong> field to view journeys initiated by a particular person.</p> </li> <li> <p><strong>Search:</strong> Use the search bar on the right to search by journey name.</p> </li> </ul> <h3 id="table-columns">Table Columns</h3> <p>The table below the filters displays key details about each journey. These columns provide insight into ownership, status, and participation.</p> <ul> <li> <p><strong>Date Created:</strong> Shows when the journey was initially created.</p> </li> <li> <p><strong>Created By:</strong> Displays the name of the user who created the journey.</p> </li> <li> <p><strong>Status:</strong> Indicates whether the journey is in Draft, Active, Paused, or Archived state.</p> </li> <li> <p><strong>Enrolled:</strong>&nbsp; Shows the number of users currently enrolled in the journey.</p> </li> <li> <p><strong>Graduated:</strong> Number of users who have completed all steps.</p> </li> </ul> <h2 id="create-journey"></h2> <h3>Learn More</h3> <p><a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108852/create-journey"><strong>Create Journey</strong></a><br>Create and design a journey from scratch, add steps, configure communication blocks, and organize the sequence of actions users follow.</p> <p><a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108853/journey-lifecycle-management"><strong>Journey Lifecycle Management</strong></a><br>Edit live journeys, pause or archive them, and review how the system behaves during each lifecycle change.</p> <p><a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108854/managing-journey-participants"><strong>Managing Journey Participants</strong></a><br>Add participants manually or automatically based on set criteria. Manage enrollment settings and remove users if needed.</p> <p><a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108855/navigating-a-journey-as-a-user"><strong>Navigating a Journey as a User</strong></a><br>View what enrolled users see as they move through a journey, including step progress, delay messages, and status notifications.</p>`,
      'create-journey': `<h1 id="create-journey">Create Journey</h1>
<p>Creating a journey involves designing a structured sequence of steps that guide users through content, tasks, or milestones. Using the drag-and-drop builder, managers and&nbsp;admins can arrange steps into a vertical flow, reorder them at any time, or group related steps into segments. Journey blocks are grouped into three categories: User Interaction, Communication, and Automation. Each category defines a different type of action users complete at a specific point in the journey.</p> <ol> <li> <p>From the Journey Manager dashboard, click<strong> Create Journey.</strong></p> <p><img alt="Click Create Journey" src="https://my.axerosolutions.com/attachment?file=Qdlv1VNMVnfjh13xTyYw3w%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="566" width="1440"></p> </li> <li> <p><strong>Enter a name</strong> for the journey, and <strong>add a description</strong> to explain its purpose and audience.</p> <p><img alt="Filled-in Name and Description fields" src="https://my.axerosolutions.com/attachment?file=8bEeGi9sK0bSkVr5SV4xRQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="570" width="1440"></p> </li> <li> <p>Design the journey flow by dragging blocks from the left panel into the canvas. Steps are stacked vertically, in the order users will follow them. Blocks are grouped into three categories:&nbsp;</p> <ul> <li><strong>User Interaction:</strong> Requires the user to view content such as articles, visit links, or read custom instructions created within the step.</li> <li><strong>Communication: </strong>Sends an email or in-app notification to the participant at the defined step.</li> <li><strong>Automation: </strong>Controls the journey flow through timed delays, space membership changes, or progress milestones.</li> </ul> </li> </ol> <h3 id="steps"><span style="color: rgba(0, 0, 0, 1)">User Interaction Steps</span></h3> <p>Steps define the individual actions a user completes throughout the journey. These can include viewing content, visiting links, or reading custom instructions. Steps can be grouped into segments to organize them by theme or phase.</p> <h4>Add Step</h4> <p>Adding a step allows you to define what the user should view or complete at a specific stage of the journey. Steps can guide participants through actions such as reading content, accessing external resources, or viewing custom instructions. Each step includes a name, description, and an optional requirement for completion before moving forward.</p> <ol> <li>From the left panel, drag the <strong>Add Step</strong> block into the journey sequence area. Once added, it will appear as a numbered step. <p><img alt="Journey example" src="https://my.axerosolutions.com/attachment?file=23zXH7EVW%2BOgyaNHLAuvcw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="642" width="1440"></p> </li> <li>Enter a step <strong>Name</strong> and <strong>Description</strong>.<br><strong></strong> <br> <p><em>Note: </em>The journey name and description you enter will be shown to participants in the My Journeys panel. Use clear, meaningful titles and descriptions to help users understand the purpose and content of the journey from the start.</p> <p><img alt="Add Step" src="https://my.axerosolutions.com/attachment?file=8iJh5PI776riXGWGuoTk9A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1200" width="1420"></p> </li> <li> <p>Select a Step Type based on what users need to view, access, or complete at this stage of the journey.</p> <ul> <li> <p><strong>View Content</strong><br>By selecting this step type, you can direct users to read existing content published on your platform. Use the filter icon to narrow your search by <strong>space</strong> and <strong>content type</strong> before entering a keyword or title in the search bar. Supported content types include: articles, blogs, files, videos, and wikis.</p> <p><em>Note:</em> If the selected content is in a private space, a prompt will appear asking whether to add a step that grants participants access to the space before they reach the content step.</p> <p><img alt="View Content" src="https://my.axerosolutions.com/attachment?file=jEyrk%2F4K83Q25NA%2BTccTHg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1186" width="1422"></p> </li> <li> <p><strong>Visit Link</strong><br>Enter a URL that directs users to an internal or external page, document, or resource they should visit as part of this step. At the bottom of the panel, check the box if you want to require completion before the user can move forward.</p> <p><img alt="Visit Link" src="https://my.axerosolutions.com/attachment?file=CYCsAhEQLrOeCNqtecATfA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1268" width="1426"></p> </li> <li> <p><strong>Custom Content</strong><br>Use the rich-text editor to create content that displays directly inside the step. This is useful for welcome messages, milestone notes, or one-off instructions. The content is only visible within the journey and is not published elsewhere.</p> <p><em>Note</em><strong>:</strong> Custom content may include external resources that cannot be automatically tracked; users must manually mark this step as complete.</p> <p><img alt="Custom Content" src="https://my.axerosolutions.com/attachment?file=WZ1Rn5zPrIFK25b5bWHzBA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1546" width="1412"></p> </li> </ul> </li> <li> <p>Click&nbsp;<strong>Save</strong> to add the step to your journey sequence.</p> </li> </ol> <h4>Add Segment</h4> <p>Adding a segment lets you organize multiple steps into a single group within the journey. Segments allow you to structure the journey by breaking it down into defined phases, helping users progress through stages such as "Week 1," "Orientation," or "Final Tasks." Each segment includes a name, description, and a setting that determines whether steps must be completed in order or in any sequence.</p> <ol> <li> <p>From the left panel, drag the <strong>Add Segment</strong> block into the sequence area. A new segment panel will appear, ready for configuration.</p> </li> <li> <p>Enter a clear name that describes the purpose of the segment, and provide a description explaining what this group of steps is about.<br><br><img alt="Step Group" src="https://my.axerosolutions.com/attachment?file=w3AuJyjlGA%2FZ53moRwQCeA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="650" width="1428"></p> </li> <li> <p>Select how users should progress through the steps within this segment:</p> <ul> <li><strong>Sequential</strong> - Steps must be completed in the defined order.</li> <li><strong>Non-sequential</strong> - Users can complete steps in any order.</li> </ul> <p><em>Note</em>: If enabled, the setting <strong>Require step completion before progression</strong> on individual steps takes precedence over the segment (step group) setting when determining the order of completion. Even if a segment is set to allow steps to be completed in any order, steps with this setting active must be completed sequentially before progressing.</p> </li> <li> <p>Add at least one step (such as Add Step or Send Email) into the segment panel.</p> <p><img alt="Add steps" src="https://my.axerosolutions.com/attachment?file=D6d25ywt8HVFTz0WK0fL%2Bw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="654" width="1440"></p> </li> <li> <p>Once steps are added and details are configured, click&nbsp;<strong>Save Journey</strong>.</p> </li> </ol> <h3 id="communication-steps">Communication Steps</h3> <p>Communication steps help you keep participants or their managers informed, engaged, or encouraged as they move through the journey. These can be used to send instructional emails, motivational reminders, or in-app alerts, helping users stay on track and informed throughout each phase.</p> <h4>Send Email</h4> <p>Configure an email message to be sent to the participant or their manager at a specific point in the journey. The message can include instructions, reminders, or motivational content aligned with the journey’s purpose.</p> <p><img alt="" src="https://my.axerosolutions.com/attachment?file=1WeUkKNppUfrNd%2BnCzrywA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1482" width="1438"></p> <ol> <li> <p>From the left panel, drag <strong>Send Email</strong> into the journey flow and place it where the email should be triggered.</p> </li> <li> <p>Give the step a clear name that reflects its purpose. <em>Example</em>: Reminder Email After Week One.</p> </li> <li> <p>Choose the recipient from the dropdown menu. You can send the email to individual participants, their manager, or a department.</p> </li> <li> <p>Compose the Email:</p> <ul> <li><strong>Subject:</strong> Enter the email subject line that will appear to recipients. Use tokens from the dropdown to personalize the message. Example: <code>Welcome, [First Name]!</code></li> <li><strong>CC / BCC (Optional):</strong> Add any additional email addresses for visibility or tracking.</li> <li><strong>Email Wrapper:</strong> Select a wrapper layout to apply a standard header/footer. This helps match your community or brand style. For more information, see how wrappers are managed in the <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/102873/email-template-manager#email-wrapper">Email Template Manager</a>.</li> <li><strong>Email Body:</strong> Use the rich-text editor to compose your message. You can format text, insert links or images, and include profile-based tokens from the "Recipient" dropdown (example: <code>City</code>, <code>Department</code>, or custom fields).</li> </ul> </li> <li> <p>Click <strong>Save Changes</strong> to add the email to your journey.</p> </li> </ol> <h4>Send Notification</h4> <p>Send a brief message to the participant through an in-app notification, email, or both. The notification is triggered when the participant reaches this point in the journey. You can use it to send reminders, brief updates, or time-sensitive alerts to help guide participants through the process.</p> <p><img alt="" src="https://my.axerosolutions.com/attachment?file=4HwtxMS4%2FGTf4hXfiZHpqA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1500" width="1424"></p> <ol> <li> <p>Drag <strong>Send Notification</strong> into the journey flow.</p> </li> <li> <p><strong>Enter a step name</strong> that clearly reflects the purpose. (<em>Example</em>: "Don’t forget to complete your next task!").</p> </li> <li> <p>Enter the notification <strong>title</strong> and <strong>message</strong>. Select a recipient token from the dropdown to personalize the message.</p> <ol></ol> </li> <li><strong>Preview</strong> how the title and message will appear to users.</li> <li> <p>Select how the notification should be delivered: in-app, by email, or both.</p> </li> <li> <p>Choose whether to send the notification after the step is reached or after a delay.</p> </li> <li> <p>Save the Step.</p> </li> </ol> <h3 id="automation-steps">Automation Steps</h3> <p>Automation steps help control the flow of the journey by managing timing, space access, and milestone recognition. They pace the experience, grant or remove access to resources, and celebrate participant progress without requiring manual action from administrators.</p> <h4>Add Delay</h4> <p>Add a delay to pause the journey for a specific amount of time before participants continue to the next step. This provides users with time to engage with content or complete previous tasks.</p> <p><em>Note</em>: Delay steps pause progress for a specified duration and are intended for use within segments (step groups) that require steps to be completed sequentially, following the defined order. When a delay step is used within a segment that allows steps to be completed in <strong>any order</strong>, the delay step temporarily locks all subsequent steps.</p> <p><img alt="Add Delay" src="https://my.axerosolutions.com/attachment?file=OOxldDHbYQNzIu1CniMhfA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="544" width="1440"></p> <ol> <li> <p>Drag <strong>Add Delay</strong> into the journey sequence, where you want the pause to occur.&nbsp;</p> </li> <li> <p>Enter the delay duration using minutes, hours, or days. The next step appears once this time passes.</p> </li> <li> <p><strong>Save the Step.</strong></p> </li> </ol> <h4 id="add-space">Add Space Access</h4> <p>Manage a participant's access to a specific space based on their progress in the journey. Use this step to grant or revoke access automatically. For example, grant temporary access to a space that contains training materials or&nbsp;onboarding tasks, then revoke the participant's access after the relevant segment is complete.</p> <p><img alt="Add Space Access" src="https://my.axerosolutions.com/attachment?file=9FqQFoifl8rpAH1oF6KDuQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="760" width="1430"></p> <ol> <li> <p>From the left panel, drag <strong>Add to Space</strong> into the journey sequence at the point where the change should occur.</p> </li> <li>Enter a name for the step.</li> <li><strong>Select</strong> the space from the dropdown menu.</li> <li> <p>Choose the action:</p> <ul> <li><strong>Add to Space:</strong> The user will automatically be added to the space at this step.</li> <li><strong>Remove from Space:</strong> The user will automatically be removed from the space when they reach this point in the journey.</li> </ul> </li> <li> <p>Click <strong>Save Changes</strong> to apply the change and continue building your journey.</p> </li> </ol> <h4>Celebrate Achievement</h4> <p>Add recognition to the journey to celebrate progress or mark key moments with a personalized message or badge. This step supports participant motivation and highlights their achievements in a meaningful way.</p> <ol> <li>From the left panel, drag the <strong>Celebrate Achievement</strong> block into the journey sequence where you want to recognize progress or completion.</li> <li>Enter a clear step name for internal tracking (example: <em>Completed Orientation</em>).</li> <li>Select a celebration type by choosing one of the following options: <ul> <li><strong>a. Notification</strong><br>If you selected <em>Notification</em>, add a short, uplifting title (example: <em>Congratulations!</em>) and write a personalized message that will appear to the user when the step is completed.<br> <p><img alt="Notification" src="https://my.axerosolutions.com/attachment?file=vYVq98lM6g1Fw2ImL2lm1A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1204" width="1428"></p> </li> <li><strong>b. Badge</strong><br>If you selected <em><a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/5952/badges">Badge</a> </em>as the celebration type, you’ll be able to award users with a digital badge when they reach this step. Choose a <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/38420/recognition-programs">Recognition Program</a> from the&nbsp;dropdown, select the badge you'd like to assign, and enter the number of points to award along with it.<br> <p><img alt="" src="https://my.axerosolutions.com/attachment?file=irsXxETaQc%2BP%2F5VigW63Eg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="892" width="1428"></p> </li> </ul> </li> <li>Click <strong>Save Changes</strong> to confirm the setup and add the step to the journey.</li> </ol>`,
      'journey-lifecycle-management': `<h1 id="journey-lifecycle-management">Journey Lifecycle Management</h1>
<p>Journeys move through several&nbsp;lifecycle stages, starting in <strong>Draft</strong> and progressing through <strong>Active</strong>, <strong>Paused</strong>, and optionally <strong>Archived</strong> or <strong>Stopped</strong>. Each status affects how the journey behaves and what actions admins can take at each stage. Understanding these states helps manage enrollment, content updates, and timing for communications.</p> <h3><img alt="Journey Manager" src="https://my.axerosolutions.com/attachment?file=apLUOOB9zGoAsXDwnnljmw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="567" width="1440"></h3> <h3>Draft</h3> <p>Every journey begins in the Draft state. It can be built and reviewed but is not yet available to participants. No steps are triggered, and no users can be enrolled.</p> <p>While in Draft, admins can:</p> <ul> <li><strong>View Journey</strong> in read-only mode.</li> <li><strong>Edit Journey</strong> to define or revise its steps, messages, and settings.<br><br><em>Note:</em> When selecting content located in a private space while editing a draft that is not yet active, or when editing a standalone draft not related to any currently active journey, a popup will appear prompting to add an "Add to Space" step if one is not already included. This ensures participants will have access to the private content once the journey is activated. Only the content inside a step can be modified - for example, replacing an article with a&nbsp;wiki. The steps themselves cannot be added, removed, or reordered.</li> <li><strong>Activate Journey</strong> to make it available to users.</li> <li><strong>Delete Journey</strong> permanently (cannot be undone).</li> <li><strong>Reassign Author</strong> to another admin if needed.</li> </ul> <h3>Active</h3> <p>Once activated, a journey becomes live. Users can be enrolled and will start progressing through the defined steps. Messages and automations are delivered as configured.</p> <p>While Active, admins can:</p> <ul> <li><strong>View Journey</strong> in read-only mode.</li> <li><strong>Edit Journey</strong> content and steps. <em>Note:</em> Only the content within a step can be changed, for example, replacing an article with a wiki. Steps themselves cannot be added, removed, or reordered once the journey is active. If the step includes private content, it can only be replaced with content from the same space, as the <a href="https://my.axerosolutions.com/spaces/133/experience-team-content/wiki/view/108741/create-journey#add-space"><strong>Add to Space</strong></a> step that grants users access to the specific content cannot be changed once the journey is active. If administrators need to reconfigure Add to Space step, they must&nbsp;<strong>stop</strong> the journey first.</li> <li><strong>Manage Users</strong>, including enrolling or removing participants.</li> <li><strong>Pause Journey</strong> to temporarily halt progress.</li> <li><strong>Stop Journey</strong> to unenroll all users and return to Draft.</li> <li><strong>Archive Journey</strong> to prevent new enrollments but keep existing progress.</li> <li><strong>Reassign Author</strong> if ownership needs to change.</li> </ul> <h3>Paused</h3> <p>Pausing a journey suspends all activity. Users stay enrolled but cannot advance, and no communications are sent.</p> <p>While Paused, admins can:</p> <ul> <li><strong>View Journey</strong> in read-only mode.</li> <li><strong>Resume Journey</strong> by setting it back to Active.</li> <li><strong>Stop Journey</strong> to return it to Draft and remove all users.</li> <li><strong>Archive Journey</strong> to preserve participant state without resuming.</li> <li><strong>Reassign Author</strong> if necessary.</li> </ul> <h3>Archived</h3> <p>When a journey is archived, it no longer accepts new participants. Existing users remain at their current step, and no further messages or automations are sent.</p> <p>While Archived, admins can:</p> <ul> <li><strong>View Journey</strong> in read-only mode.</li> <li><strong>Copy Journey</strong> to start a new draft from the same structure.</li> <li><strong>Reassign Author</strong> for visibility or ownership updates.</li> </ul> <h3>Stopped</h3> <p>Stopping a journey removes all participants and returns the journey to Draft. It is used when the journey needs to be reset or discontinued.</p> <p>After stopping, the journey reverts to the <strong>Draft</strong> state, with all corresponding actions available:</p> <ul> <li><strong>View</strong>, <strong>Edit</strong>, <strong>Activate</strong>, <strong>Delete</strong>, and <strong>Reassign Author</strong>.<br>Participant progress is not retained.</li> </ul>`,
      'managing-journey-participants': `<h1 id="managing-journey-participants">Managing Journey Participants</h1>
<p>Admins can manually control who participates in a journey. Once a journey is activated, users can be added or removed at any time through the <strong>Manage Users</strong> tab.</p> <h2 id="enrolling-users">Enroll Users in a Journey</h2> <p>Journeys must be activated before users can be added. Once active, a <strong>Manage Users</strong> tab appears in the journey editor.&nbsp; Enrolled users will immediately begin the journey and progress through the steps as defined in the builder.&nbsp;</p> <p><strong>To enroll users:</strong></p> <ol> <li>From the Journey Manager, click the three-dot menu (⋮) next to a drafted journey and select <strong>Activate Journey</strong>.<br><br><img alt="Click Activate Journey" src="https://my.axerosolutions.com/attachment?file=q5ZpndoHU%2B47FLfr8A8PHA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="604" width="1440"></li> <li>A prompt will appear confirming that users will be enrolled and messages sent according to the journey setup. Click&nbsp;<strong>Yes</strong> to proceed.<br><br><img alt="Click Yes" src="https://my.axerosolutions.com/attachment?file=zdwhWL8hmcV6%2BsPuZR7CiQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="706" width="1440"></li> <li> <p>After activation, click the three-dot menu (⋮) and select <strong>Manage Users</strong> to open the user management view.<br><br><img alt="Manage Users" src="https://my.axerosolutions.com/attachment?file=lKXNXS1TEjmXk6JpXn%2FYbQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="380" width="1440"></p> </li> <li> <p>Click <strong>Add Users</strong> to open the user selection window.<br><br><img alt="Click Add Users" src="https://my.axerosolutions.com/attachment?file=vOm8c7wrVNsYV0%2BQWG0dXA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="934" width="1430"></p> </li> <li> <p>Use the&nbsp;<strong>+ Add</strong> button to add individuals, or select multiple users and click <strong>Add Selected</strong>. You can also use the search bar to find specific users.</p> </li> <li> <p>Click <strong>Save Journey</strong> to confirm enrollment.</p> </li> </ol> <h2>Removing Participants</h2> <p>Admins can remove users from a journey at any time. When removed, users lose access to all journey steps. If re-enrolled later, they will start from the beginning.</p> <p>To remove a participant:</p> <ol> <li> <p>Go to the <strong>Manage Users</strong> tab in the active journey.</p> </li> <li> <p>Find the user you want to remove in the list.<br><br><img alt="Manage Users" src="https://my.axerosolutions.com/attachment?file=GKIAG%2FNf7hYDRiHYrdITtw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="598" width="1440"></p> </li> <li> <p>Click the red trash can icon next to their progress bar.</p> </li> <li> <p>Confirm the removal in the prompt.</p> </li> <li> <p>Click <strong>Save Journey</strong> to apply changes.</p> </li> </ol> <p>The user will be removed from the journey and will no longer appear in the participant list.</p> <h3>Tracking User Progress</h3> <p>To see how each user is progressing through the journey:</p> <ol> <li> <p>Go to <strong>Manage Users</strong>.<br><br><img alt="Manage Users" src="https://my.axerosolutions.com/attachment?file=mKgx2GPxQcQQ0crCxt641A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="567" width="1440"><em><br></em></p> </li> <li> <p>Click on a user’s progress bar or name to open their detailed view.<em><br></em></p> </li> </ol> <p><img alt="Participant progress" src="https://my.axerosolutions.com/attachment?file=Qj%2BigZGp5xH%2BXP7bLzL%2FcA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="796" width="1434"></p> <p>This displays their start date, current step, time in step, and overall progress.</p> <p><strong>Note</strong>:</p> <ul> <li> <p>If a journey is <strong>paused</strong>, no new users can be enrolled. Existing participants remain in place and will resume progress once the journey is active again.&nbsp;</p> </li> <li> <p>If a journey is <strong>stopped</strong>, all participants are removed, and the journey returns to<strong> Draft</strong> mode.</p> </li> <li> <p>If a user is <strong>removed</strong> and re-enrolled later, they will start from the beginning of the journey.</p> </li> </ul> <h2 id="user-experience"></h2>`,
      'navigating-a-journey-as-a-user': `<h1 id="navigating-a-journey-as-a-user">Navigating a Journey as a User</h1>
<p>Once a user is enrolled in a Journey, they gain access to a personalized interface that tracks their progress and guides them through each step.</p> <h3>Accessing your Journey</h3> <p>After enrollment, users receive a notification by email and/or in-app. The message includes the journey name, a brief description, and a link to open the&nbsp;<strong>My Journeys</strong> panel. From there, users can see all active journeys they are participating in, view their current step, and resume progress where they last left off.</p> <p><img alt="Notifications" src="https://my.axerosolutions.com/attachment?file=plrZVC9eBazdEcZz1iiD1w%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="625" width="1440"></p> <p>Users can also access their journeys at any time from the top navigation bar. Click the&nbsp;<strong>journey icon</strong> to open the <strong>My Journeys</strong> panel.</p> <p><img alt="Click Journey icon" src="https://my.axerosolutions.com/attachment?file=k4f5SmaXSCdSFAOiXvKAeg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="657" width="1440"></p> <p>This opens a sidebar with all active journeys, showing their progress, current step, and a button to continue where they left off.<br><br><img alt="View Journeys" src="https://my.axerosolutions.com/attachment?file=ETEUx%2FeITp6Qhgo7pG3fFw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="642" width="1440"></p> <h3>Tracking Progress</h3> <p>When a user opens a journey, a progress bar appears at the top showing the percentage of steps completed. Each step is displayed vertically in the order it was defined. Completed steps are clearly marked and can be revisited, while the current step is visually highlighted. Future steps remain locked if they depend on a previous step or are set to appear after a delay.</p> <p>If a step has a required delay (e.g., a 3-day wait), the next step remains locked and displays a message with the remaining time. Once the delay expires, users receive a notification that the next step is available.</p> <p><img alt="Viewing progress" src="https://my.axerosolutions.com/attachment?file=2oCoXgOTjTZAOPYCRfyC8w%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="689" width="1440"></p> <h3>Completing Steps</h3> <p>Each step includes a&nbsp;<strong>Mark as Complete</strong>&nbsp;button that becomes visible once the required action has been completed. This could involve reading content, watching a video, or another predefined activity. When the user marks the step as complete, the interface updates to reflect the change. The progress bar advances, and the next step unlocks, provided no delay or dependency is still in effect.</p> <p>Steps can be completed in any order unless they are part of a segment that requires sequential completion or if a preceding step is marked as "Require step completion before progression."</p> <p><img alt="Mark as completed" src="https://my.axerosolutions.com/attachment?file=ozlTU5edKdsmLE74Gdx9rg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="788" width="1440"></p> <h3>Status Changes and Notifications</h3> <p>If a journey is paused, users see it labeled as <strong>Paused</strong> in the My Journeys panel and receive a notification. They cannot proceed until it is resumed, but can still view completed steps.</p> <p>If a journey is stopped or archived, users are notified and the journey is removed from their active list. In both cases, access is revoked unless the user is re-enrolled in the future.</p>`,
      'transitioning-to-jwt-authentication': `<h1 id="transitioning-to-jwt-authentication">Transitioning to JWT Authentication</h1>
<p>Beginning with <strong>Axero version 9.45</strong>, the platform is modernizing REST API authentication by transitioning from traditional API keys to <strong>JWT (JSON Web Token) Bearer Tokens</strong>. JWT is an industry-standard method for securely transmitting information between parties.</p> <p><strong>✅ New:</strong> All REST API calls will be authenticated using <strong>JWT Bearer Tokens</strong> with the standard format <code>Authorization: Bearer {token}</code>.</p> <p><strong>❌ Deprecated:</strong> Legacy REST API keys will no longer be visible or editable in the user interface. Existing API keys will continue to function until <strong>November 2025</strong>.</p> <hr> <h3><strong>Why This Change Benefits You</strong></h3> <p>This modernization brings significant security and operational advantages for your organization:</p> <ul> <li> <p><strong>Enhanced Security</strong> – JWT tokens use cryptographic signatures and configurable expiration times to reduce risks from credential theft, token replay attacks, and unauthorized access.</p> </li> <li> <p><strong>Greater Flexibility</strong> – Create unlimited tokens with custom names, expiration periods, and specific purposes. Each token can be tailored to individual applications, integrations, or user roles.</p> </li> <li> <p><strong>Improved Management</strong> – Gain complete visibility into active integrations with detailed token metadata, usage tracking, and the ability to instantly revoke access without affecting other integrations.</p> </li> <li> <p><strong>Industry Standards Compliance</strong> – JWT tokens align with modern authentication standards, ensuring compatibility with enterprise security tools and third-party integrations.</p> </li> </ul> <hr> <h3><strong>API Key vs. JWT Token Comparison</strong></h3> <div align="left"> <table><colgroup> <col style="width: 160px"> <col style="width: 217px"> <col style="width: 217px"> </colgroup> <thead> <tr> <th scope="col" style="vertical-align: top"> <p><strong>Feature</strong></p> </th> <th scope="col" style="vertical-align: top"> <p><strong>API Key</strong></p> </th> <th scope="col" style="vertical-align: top"> <p><strong>JWT Token (Bearer Token)</strong></p> </th> </tr> </thead> <tbody> <tr> <td style="vertical-align: top"> <p><strong>Visibility After Creation</strong></p> </td> <td style="vertical-align: top"> <p>Always visible in user preferences (stored in database)</p> </td> <td style="vertical-align: top"> <p>Displayed only once at creation for security; not stored by Axero after generation</p> </td> </tr> <tr> <td style="vertical-align: top"> <p><strong>Expiration Control</strong></p> </td> <td style="vertical-align: top"> <p>No expiration (permanent until manually deleted)</p> </td> <td style="vertical-align: top"> <p>Configurable expiration periods: minutes to years, or unlimited (if enabled by admin)</p> </td> </tr> <tr> <td style="vertical-align: top"> <p><strong>Revocation</strong></p> </td> <td style="vertical-align: top"> <p>Manual deletion required; immediate effect</p> </td> <td style="vertical-align: top"> <p>Instant revocation through UI; immediately invalidates all requests using that token</p> </td> </tr> <tr> <td style="vertical-align: top"> <p><strong>Security Level</strong></p> </td> <td style="vertical-align: top"> <p>Static credentials with no built-in expiration; higher risk if compromised</p> </td> <td style="vertical-align: top"> <p>Cryptographically signed with configurable expiration</p> </td> </tr> <tr> <td style="vertical-align: top"> <p><strong>Scope &amp; Permissions</strong></p> </td> <td style="vertical-align: top"> <p>Inherits all permissions from the user account that created it</p> </td> <td style="vertical-align: top"> <p>Inherits user permissions but can be customized with descriptive names for specific applications or integration purposes</p> </td> </tr> <tr> <td style="vertical-align: top"> <p><strong>Token Management &amp; Tracking</strong></p> </td> <td style="vertical-align: top"> <p>Basic visibility; no naming or categorization options</p> </td> <td style="vertical-align: top"> <p>Full lifecycle management: custom names, creation dates, expiration tracking, and usage monitoring</p> </td> </tr> </tbody> </table> </div> <h3><strong>Frequently Asked Questions</strong></h3> <p><strong>Q: Do I need to do anything if our organization doesn't currently use REST API keys?</strong><br><strong>A:</strong> No immediate action is required. However, if you plan to use the REST API in the future, JWT Bearer Tokens will be the only supported authentication method after November 2025.</p> <p><strong>Q: Can I test JWT tokens before fully switching over?</strong><br><strong>A:</strong> Yes. Both authentication methods work simultaneously during the transition period, allowing you to test JWT tokens in development environments and gradually migrate production integrations without service interruption.</p> <p><strong>Q: Can I view and manage my JWT tokens after creating them?</strong><br><strong>A:</strong> You can view token metadata (name, creation date, expiration date, status) and manage tokens (rename, revoke) by navigating to your <strong>Profile &gt; Activity Stream &gt; Integrations &gt; Authorizations</strong>. However, the actual token value cannot be retrieved after initial creation for security reasons.</p> <p><strong>Q: What should I do if I lose a JWT token?</strong><br><strong>A:</strong> If you lose a token, you must create a new one and update all applications that use it. The lost token should be revoked immediately to maintain security. This is why secure storage of tokens is critical.</p> <p><strong>Q: Can I use the same JWT token across multiple applications?</strong><br><strong>A:</strong> While technically possible, it's a security best practice to create separate tokens for each application or integration. This allows for better tracking, individual revocation, and follows the principle of least privilege.</p> <hr> <h3><strong>Next Steps</strong></h3> <p>Ready to transition to JWT authentication? Follow this recommended migration path:</p> <ol> <li><strong>Plan Your Migration:</strong> Inventory all current API key usage and create a migration timeline</li> <li><strong>Enable JWT Support:</strong> Work with your administrator to <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108972/enabling-api-access">enable JWT authentication</a></li> <li><strong>Create Test Tokens:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108973/creating-bearer-tokens">Generate JWT tokens</a> for development and testing</li> <li><strong>Update Applications:</strong> Modify your integrations to use the new authentication format</li> <li><strong>Test Thoroughly:</strong> Verify all functionality works correctly with JWT tokens</li> <li><strong>Deploy Gradually:</strong> Migrate production systems one at a time to minimize risk</li> <li><strong>Monitor and Optimize:</strong> Track token usage and implement security best practices</li> <li><strong>Complete Migration:</strong> Revoke legacy API keys once all systems are successfully migrated</li> </ol> <p>For detailed implementation guidance, see <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108975/using-the-rest-api">Using the REST API</a>.</p>`,
      'enabling-api-access': `<h1 id="enabling-api-access">Enabling API Access</h1>
<p>Before users can create JWT Bearer Tokens and access the Axero REST API, an administrator must enable API access and configure appropriate security settings. This guide walks through the complete setup process, from enabling JWT support to configuring security levels that match your organization's requirements.</p> <blockquote>📋 <strong>Prerequisites:</strong> You must have administrator privileges to configure REST API settings. These changes affect all users in your organization and should be planned accordingly.</blockquote> <hr> <h3>Step 1: Enable JWT Bearer Token Support</h3> <p>To enable JWT authentication and unlock advanced security options, you need to configure specific system properties:</p> <ol> <li><strong>Navigate to System Properties:</strong> Go to <strong>Control Panel &gt; System &gt; System Properties</strong></li> <li><strong>Configure JWT Settings:</strong> Set the following properties to enable JWT Bearer Token functionality: <ul> <li><code>EnableLegacyAPIKey = false</code> - Disables legacy API key creation in user preferences</li> <li><code>EmulateLegacyAPIKey = true</code> - Enables JWT token management in the Integrations section</li> </ul> </li> <li><strong>Save Changes:</strong> Apply the configuration to activate JWT support</li> </ol> <p><img alt="Enable JWT Settings" src="https://my.axerosolutions.com/attachment?file=Ddt%2BQVmIexTCSmHH5gnw7g%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="370" style="margin: 0" width="1435" data-action="zoom"></p> <hr> <h3>Step 2: Configure Security Level</h3> <p>After enabling JWT support, configure the appropriate security level for your organization. Navigate to <strong>REST API Settings</strong> and choose from three security options, each offering different levels of protection and compatibility:</p> <h3>🔁 Legacy Security (Transitional)</h3> <p>Maintains backward compatibility by supporting both legacy API keys and JWT tokens during the migration period.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 0"> <div> <h4>✅ Supported Features</h4> <ul> <li>Legacy API keys (read-only)</li> <li>JWT Bearer Tokens</li> <li>Username/password authentication</li> <li>All existing integrations continue working</li> </ul> </div> <div> <h4>⚠️ Important Considerations</h4> <ul> <li>Recommended for transition period only</li> <li>Legacy support ends <strong>November 2025</strong></li> <li>Automatic upgrade to High Security after deprecation</li> <li>Plan migration to JWT tokens during this phase</li> </ul> </div> </div> <p><img alt="Legacy Security Screenshot" src="https://my.axerosolutions.com/attachment?file=t5yDLLNphlwXMdXnB7kX9Q%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="899" style="margin: 0" width="1435" data-action="zoom"></p> <h3>🔐 High Security (Recommended)</h3> <p>The recommended security level that eliminates legacy API keys while maintaining compatibility for essential third-party integrations. This mode provides enhanced JWT token management with configurable expiration controls.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 0"> <div> <h4>✅ Supported Features</h4> <ul> <li>JWT Bearer Tokens with full lifecycle management</li> <li>Username/password authentication for third-party tools</li> <li>Configurable token expiration settings</li> <li>Enhanced security monitoring and controls</li> </ul> </div> <div> <h4>❌ Disabled Features</h4> <ul> <li>Legacy API key creation and editing</li> <li>API key visibility in user preferences</li> <li>Existing API keys stop functioning</li> </ul> </div> </div> <p><img alt="High Security Screenshot" src="https://my.axerosolutions.com/attachment?file=45U90UIF2vsMSuMKBEPGuA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="861" style="margin: 0" width="1435" data-action="zoom"></p> <blockquote>⚠️ <strong>Migration Required:</strong> Before enabling High Security, ensure all applications using legacy API keys are updated to use JWT Bearer Tokens. Legacy API keys will immediately stop functioning.</blockquote> <h4>JWT Token Expiration Configuration</h4> <p>High Security mode provides granular control over token expiration policies:</p> <ul> <li><strong>Default Expiration:</strong> Set a default expiration period that pre-fills when users create new tokens (e.g., 1 year, 6 months)</li> <li><strong>Maximum Expiration:</strong> Enforce an organizational maximum for token duration to maintain security compliance</li> <li><strong>Allow Unexpiring Tokens:</strong> Optionally permit tokens without expiration dates for specific use cases (use with caution)</li> </ul> <div style="background: rgba(255, 243, 205, 1); border: 1px solid rgba(255, 234, 167, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🔧 Configuration Best Practices:</strong></p> <ul> <li><strong>Default Expiration:</strong> Set to 1 year for most organizations, shorter for high-security environments</li> <li><strong>Maximum Expiration:</strong> Consider 2-3 years maximum to balance security and operational needs</li> <li><strong>Unexpiring Tokens:</strong> Only enable if absolutely necessary for critical system integrations</li> </ul> </div> <h3>🔒 Extra High Security (Maximum Protection)</h3> <p>The highest security level that provides maximum protection by requiring JWT Bearer Tokens for all API access. This mode completely disables username/password authentication, ensuring all API requests use cryptographically signed tokens.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 0"> <div> <h4>✅ Enhanced Security Features</h4> <ul> <li>JWT Bearer Tokens only — no password authentication</li> <li>Maximum cryptographic security</li> <li>Complete audit trail for all API access</li> <li>Eliminates password-based attack vectors</li> <li>Ideal for high-security environments</li> </ul> </div> <div> <h4>⚠️ Compatibility Limitations</h4> <ul> <li>Third-party tools requiring username/password will not work</li> <li>Official mobile app becomes non-functional</li> <li>All integrations must support JWT tokens</li> <li>Requires comprehensive testing before implementation</li> </ul> </div> </div> <p><img alt="Extra High Security Screenshot" src="https://my.axerosolutions.com/attachment?file=k5i%2BJ6R4yUK90Kb%2FQIOlUA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="857" style="margin: 0" width="1435" data-action="zoom"></p> <div style="background: rgba(248, 215, 218, 1); border: 1px solid rgba(245, 198, 203, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🚨 Critical Considerations Before Enabling:</strong></p> <ul> <li><strong>Mobile App Impact:</strong> The official Axero mobile app will stop working until it's updated to support JWT authentication</li> <li><strong>Third-Party Integration Testing:</strong> Verify all external tools and integrations support JWT Bearer Tokens</li> <li><strong>User Training:</strong> Ensure users understand the new authentication requirements</li> <li><strong>Rollback Plan:</strong> Have a plan to revert to High Security if issues arise</li> </ul> </div> <hr> <h3>Step 3: Enable User Role Permissions</h3> <p>After configuring security levels, ensure that user roles have the appropriate REST API permissions:</p> <ol> <li><strong>Navigate to Role Management:</strong> Go to <strong>Control Panel &gt; Users &gt; Roles</strong></li> <li><strong>Select User Roles:</strong> Choose the roles that should have REST API access</li> <li><strong>Enable API Permissions:</strong> Grant "REST API Access" permission to selected roles</li> <li><strong>Save Changes:</strong> Apply the permission changes</li> </ol> <blockquote>🔑 <strong>Permission Note:</strong> Users without REST API permissions will not see the Authorizations section in their account settings and cannot create JWT tokens.</blockquote> <p>Once JWT support is enabled and user permissions are configured, users can create their own JWT Bearer Tokens: <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108973/creating-bearer-tokens">Generating JWT Bearer Tokens</a></p> <hr> <h3>Next Steps</h3> <p>After completing the REST API setup:</p> <ol> <li><strong>Test Configuration:</strong> Verify that authorized users can access the Authorizations section</li> <li><strong>Create Test Tokens:</strong> Have users create test JWT tokens to validate functionality</li> <li><strong>Update Documentation:</strong> Inform your team about the new token creation process</li> <li><strong>Monitor Usage:</strong> Track token creation and usage patterns for security compliance</li> </ol>`,
      'creating-bearer-tokens': `<h1 id="creating-bearer-tokens">Creating Bearer Tokens</h1>
<p>JWT Bearer Tokens provide enhanced security and management capabilities for REST API authentication in Axero. Available starting with <strong>Axero version 9.45</strong>, these cryptographically signed tokens replace traditional API keys and follow industry-standard protocols (RFC 7519) for secure API access.</p> <blockquote>📋 <strong>Administrator Prerequisites:</strong> JWT Bearer Token functionality must be enabled by an administrator through <strong>Control Panel &gt; System &gt; System Properties</strong> with the settings <code>EnableLegacyAPIKey = false</code> and <code>EmulateLegacyAPIKey = true</code>. See <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108972/enabling-api-access">Enabling REST API Access</a> for complete setup instructions.</blockquote> <blockquote>🔑 <strong>User Permission Requirements:</strong> Your user role must have REST API access enabled by an administrator. If you cannot access the Authorizations section, contact your system administrator to enable API permissions for your role.</blockquote> <p>Once prerequisites are met, users can create and manage JWT Bearer Tokens through the <strong>Authorizations</strong> section in their account settings. This self-service interface provides secure token lifecycle management for API integrations with external applications and tools.</p> <hr> <h3>Creating Your First JWT Bearer Token</h3> <p>Follow these step-by-step instructions to create a new JWT Bearer Token for your API integrations:</p> <ol> <li><strong>Access Your Profile Menu:</strong> Click on your profile avatar in the top right corner of the screen to open the user menu.<br><br><img alt="Access your profile menu" src="https://my.axerosolutions.com/attachment?file=4LVxpscW%2Bat9nsfdW8anHQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="732" width="1440"></li> <li><strong>Navigate to My Account:</strong> From the dropdown menu, select <strong>Activity Stream</strong> to access your account settings.<br><br><img alt="Go to activity stream" src="https://my.axerosolutions.com/attachment?file=TZ2a97LxTfO5E%2FWgcnnOqQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="742" width="1440"></li> <li><strong>Open Integrations Section:</strong> In the left sidebar navigation, locate and click <strong>Integrations</strong>.<br><br><img alt="Go to Integrations" src="https://my.axerosolutions.com/attachment?file=MYZGiqj7EKadjoUi2RYFKA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="780" width="1440"></li> <li><strong>Access Authorizations:</strong> Within the Integrations section, navigate to the <strong>Authorizations</strong> tab. This is your JWT Bearer Token management center where you can create, view, and manage all your API tokens.</li> <li><strong>Initiate Token Creation:</strong> Click the <strong>Add Authorization</strong> button. A new token configuration form will appear at the top of your token list.<br><br><img alt="Click the Add autorization button" src="https://my.axerosolutions.com/attachment?file=b%2FCpgjstG7jhr73SEPKkoA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="659" width="1440"></li> <li><strong>Configure Token Settings:</strong> Complete the token configuration form with the following information: <h4>Token Name (Required)</h4> <p>Enter a descriptive, meaningful name that clearly identifies the token's purpose and intended use. Good naming practices help with organization and security auditing.</p> <div style="background: rgba(248, 249, 250, 1); border-left: 4px solid rgba(40, 167, 69, 1); padding: 15px; margin: 15px 0"> <p><strong>✅ Good Token Name Examples:</strong></p> <ul> <li><strong>"Analytics Dashboard Integration"</strong> - Specific purpose and system</li> <li><strong>"Third-party CRM Sync - Production"</strong> - Purpose and environment</li> <li><strong>"Automated Reporting System - Q4 2025"</strong> - Purpose and timeframe</li> <li><strong>"Mobile App Backend - iOS"</strong> - Application and platform</li> </ul> </div> <h4>Creation Date</h4> <p>Automatically populated with the current date and time when the token is generated. This helps track token age and lifecycle.</p> <h4>Expiration Period</h4> <p>Select an appropriate expiration timeframe based on your integration needs. Available options depend on your administrator's security settings:</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h5>🕐 Short-term (Minutes to Hours)</h5> <ul> <li><strong>Best for:</strong> Testing, development, temporary access</li> <li><strong>Security:</strong> Highest - minimal exposure window</li> <li><strong>Use cases:</strong> API testing, proof of concepts</li> </ul> </div> <div> <h5>📅 Medium-term (Days to Months)</h5> <ul> <li><strong>Best for:</strong> Project-based integrations, seasonal applications</li> <li><strong>Security:</strong> Balanced - defined lifecycle</li> <li><strong>Use cases:</strong> Campaign tools, temporary dashboards</li> </ul> </div> </div> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h5>📆 Long-term (Years)</h5> <ul> <li><strong>Best for:</strong> Stable, long-running integrations</li> <li><strong>Security:</strong> Moderate - requires monitoring</li> <li><strong>Use cases:</strong> Production systems, core integrations</li> </ul> </div> <div> <h5>♾️ Unlimited (No Expiration)</h5> <ul> <li><strong>Best for:</strong> Critical systems requiring continuous access</li> <li><strong>Security:</strong> Requires careful management</li> <li><strong>Availability:</strong> Only if enabled by administrator</li> </ul> </div> </div> </li> <li><strong>Generate the Token:</strong> After configuring all settings, click the <em class="fas fa-check-circle"></em> checkmark icon to save your configuration and generate the JWT Bearer Token.<br><br><img alt="Add Authorization" src="https://my.axerosolutions.com/attachment?file=qzD0oRWO8pF5aMzp2EAruw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="688" width="1440"></li> <li><strong>Secure Token Storage:</strong> The JWT Bearer Token is displayed immediately after generation. <strong>This is your only opportunity to view the complete token value.</strong> <div style="background: rgba(255, 243, 205, 1); border: 1px solid rgba(255, 234, 167, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🔐 Critical Security Step:</strong></p> <ul> <li>Use the clipboard icon to copy the complete token value</li> <li>Store it immediately in a secure location (password manager, encrypted vault, secure configuration)</li> <li>Verify the token is saved before closing this window</li> <li>The token cannot be retrieved again after this step</li> </ul> </div> <img alt="Token" src="https://my.axerosolutions.com/attachment?file=XTFZzNCvCeMq%2BWG99kewqQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="581" width="1440"></li> </ol> <blockquote>⚠️ <strong>Critical Security Reminder:</strong> JWT Bearer Tokens cannot be retrieved after the initial display. The token value is not stored by Axero for security reasons. If you lose a token, you must create a new one and update all applications that use it. Plan accordingly and store tokens in a secure, accessible location.</blockquote> <hr> <h3>Using Your JWT Bearer Token</h3> <p>Once you've created and securely stored your JWT Bearer Token, you can use it to authenticate API requests. The token must be included in the <code>Authorization</code> header of every API call.</p> <h4>Authentication Header Format</h4> <pre><code>Authorization: Bearer YOUR-JWT-TOKEN-HERE</code></pre> <p>Replace <code>YOUR-JWT-TOKEN-HERE</code> with the actual token value you copied during creation.</p> <h4>Example API Requests</h4> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>📝 cURL Example:</strong></p> <pre><code>curl -X GET "https://yoursite.axero.com/api/users/me" \\ -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \\ -H "Content-Type: application/json"</code></pre> </div> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🌐 JavaScript (fetch) Example:</strong></p> <pre><code>fetch('https://yoursite.axero.com/api/users/me', { method: 'GET', headers: { 'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', 'Content-Type': 'application/json' } }) .then(response =&gt; response.json()) .then(data =&gt; console.log(data));</code></pre> </div> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🐍 Python (requests) Example:</strong></p> <pre><code>import requests headers = { 'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', 'Content-Type': 'application/json' } response = requests.get('https://yoursite.axero.com/api/users/me', headers=headers) data = response.json()</code></pre> </div> <hr> <h3>Managing Existing Tokens</h3> <p>The Authorizations section provides comprehensive lifecycle management for all your JWT Bearer Tokens. You can monitor token status, update organization, and maintain security through proper token hygiene.</p> <h4>Token Information Dashboard</h4> <p>For each token in your Authorizations list, you can view important metadata to help manage your API integrations:</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h5>📋 Available Information</h5> <ul> <li><strong>Token Name:</strong> The descriptive name you assigned during creation</li> <li><strong>Creation Date:</strong> When the token was originally generated</li> <li><strong>Expiration Date:</strong> When the token will automatically expire (if applicable)</li> <li><strong>Status:</strong> Whether the token is active, expired, or revoked</li> </ul> </div> <div> <h5>🔒 Security Features</h5> <ul> <li><strong>Token Value:</strong> Never displayed after creation</li> <li><strong>Usage Tracking:</strong> Monitor when tokens are accessed</li> <li><strong>Instant Revocation:</strong> Immediately disable compromised tokens</li> <li><strong>Expiration Monitoring:</strong> Track approaching expiration dates</li> </ul> </div> </div> <blockquote>🔍 <strong>Security Note:</strong> The actual token value is never displayed after initial creation for enhanced security. Only metadata about the token is shown in the management interface. This prevents accidental exposure and ensures tokens remain secure.</blockquote> <h4>Renaming a Token</h4> <p>Keep your token organization current by updating names to reflect changes in purpose, environment, or usage:</p> <div style="background: rgba(240, 248, 255, 1); border-left: 4px solid rgba(0, 102, 204, 1); padding: 15px; margin: 15px 0"> <p><strong>💡 When to Rename Tokens:</strong></p> <ul> <li>Application or integration purpose changes</li> <li>Moving from development to production environment</li> <li>Transferring token ownership between teams</li> <li>Improving naming consistency across your organization</li> </ul> </div> <ol> <li><strong>Locate the Token:</strong> Find the token you want to rename in your Authorizations list</li> <li><strong>Enter Edit Mode:</strong> Click the <em class="fal fa-pencil"></em> pencil icon next to the token name</li> <li><strong>Update the Name:</strong> Enter a new descriptive name that clearly identifies the token's current purpose</li> <li><strong>Save Changes:</strong> Click the <em class="fas fa-check-circle"></em> checkmark icon to save your changes</li> </ol> <p><img alt="Rename token" src="https://my.axerosolutions.com/attachment?file=qoCgck6mixQEi2cnB%2BxbXg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="803" style="margin: 0" width="1435" data-action="zoom"></p> <h4>Revoking a Token</h4> <p>Immediately and permanently disable a token when it's no longer needed or if security has been compromised. This is a critical security operation that should be performed carefully.</p> <div style="background: rgba(248, 215, 218, 1); border: 1px solid rgba(245, 198, 203, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🚨 When to Revoke Tokens:</strong></p> <ul> <li><strong>Security Compromise:</strong> Token may have been exposed or stolen</li> <li><strong>Integration Decommissioning:</strong> Application or service is being retired</li> <li><strong>Team Member Departure:</strong> Employee with token access leaves organization</li> <li><strong>Token Rotation:</strong> Replacing with new token as part of security policy</li> <li><strong>Suspicious Activity:</strong> Unusual API usage patterns detected</li> </ul> </div> <ol> <li><strong>Identify the Token:</strong> Find the token you want to disable in your Authorizations list</li> <li><strong>Initiate Revocation:</strong> Click the <strong>trash icon</strong> next to the token entry to revoke it</li> <li><strong>Confirm Action:</strong> Review the confirmation dialog and confirm the revocation</li> <li><strong>Immediate Effect:</strong> The token is instantly invalidated - all API calls using it will fail with authentication errors</li> </ol> <p><img alt="Revoke" src="https://my.axerosolutions.com/attachment?file=oPoAUa0I%2BEIrdUoc2w%2FTog%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="580" width="1440"></p> <blockquote>⚠️ <strong>Critical Warning:</strong> Token revocation is immediate and irreversible. Before revoking a token, ensure all applications using it are updated with a replacement token to prevent service disruptions. Consider creating and testing the new token before revoking the old one.</blockquote> <hr> <h3>Token Security Best Practices</h3> <p>Following these security guidelines ensures your JWT Bearer Tokens remain secure and your API integrations are protected:</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0"> <div> <ul style="list-style: none; margin-left: 5px"> <li>✅ Store tokens securely (environment variables, secure vaults)</li> <li>✅ Use HTTPS for all API requests</li> <li>✅ Implement token rotation and refresh mechanisms</li> <li>✅ Monitor token usage and revoke compromised tokens</li> <li>✅ Regularly review and revoke unused tokens</li> <li>✅ Set shorter expiration periods for development/testing</li> <li>✅ Use descriptive names that include the application or service</li> <li>✅ Keep an inventory of active tokens for security auditing</li> </ul> </div> <div> <ul style="list-style: none; margin-left: 5px"> <li>❌ Never commit tokens to version control</li> <li>❌ Don't expose tokens in client-side code</li> <li>❌ Don't share tokens between different applications</li> <li>❌ Don't ignore token expiration warnings</li> <li>❌ Don't use tokens with overly broad permissions</li> <li>❌ Don't store tokens in plain text files</li> <li>❌ Don't forget to revoke tokens when no longer needed</li> </ul> </div> </div> <div style="background: rgba(255, 243, 205, 1); border: 1px solid rgba(255, 234, 167, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🔒 Additional Security Recommendations:</strong></p> <ul> <li><strong>Token Rotation:</strong> Implement regular token rotation schedules for long-running integrations</li> <li><strong>Access Monitoring:</strong> Log and monitor API access patterns to detect unusual activity</li> <li><strong>Principle of Least Privilege:</strong> Only grant the minimum permissions necessary for each integration</li> <li><strong>Incident Response:</strong> Have a plan for quickly revoking and replacing tokens if security is compromised</li> </ul> </div> <h3>Next Steps</h3> <p>After creating your JWT Bearer Token, follow these steps to ensure successful implementation:</p> <ol> <li><strong>Test the Token:</strong> Verify it works with a simple API call using tools like Postman, curl, or your development environment</li> <li><strong>Update Applications:</strong> Replace any legacy API keys with the new JWT token in your application configurations</li> <li><strong>Document Token Usage:</strong> Record which applications and services are using each token for future reference</li> <li><strong>Monitor Usage:</strong> Regularly review token activity and expiration dates in the Authorizations section</li> <li><strong>Plan Renewals:</strong> Set calendar reminders before tokens expire to ensure uninterrupted service</li> </ol>`,
      'using-the-rest-api': `<h1 id="using-the-rest-api">Using the REST API</h1>
<p>This guide covers the practical implementation of the Axero REST API, including JWT Bearer Token authentication, performance optimization, and security best practices. Whether you're migrating from legacy API keys or building new integrations, this documentation provides the essential knowledge for successful API implementation.</p> <hr> <h2>Authentication with JWT Bearer Tokens</h2> <p>The Axero REST API uses <strong>JWT Bearer Tokens</strong> for secure authentication. These cryptographically signed tokens provide enhanced security compared to traditional API keys and follow industry standards.</p> <h3>Basic Authentication Format</h3> <p>After you <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108973/creating-bearer-tokens">create your JWT token</a>, include it in the <code>Authorization</code> header of all API requests:</p> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>📝 Request Format</strong></p> <pre>GET /api/users/me Host: yoursite.axero.com Authorization: Bearer YOUR_JWT_TOKEN_HERE Content-Type: application/json</pre> </div> <h3>Migrating from Legacy API Keys</h3> <p>The primary change involves updating the authorization header format in all your API requests:</p> <div style="background: rgba(255, 243, 205, 1); border: 1px solid rgba(255, 234, 167, 1); border-radius: 8px; padding: 15px"> <p><strong>❌ Legacy Format</strong></p> <pre><code>Authorization: rest-api-key YOUR_LEGACY_KEY_HERE</code></pre> </div> <div style="margin-top: 15px; background: rgba(212, 237, 218, 1); border: 1px solid rgba(195, 230, 203, 1); border-radius: 8px; padding: 15px"> <p><strong>✅ New JWT Format</strong></p> <pre><code>Authorization: Bearer YOUR_JWT_TOKEN_HERE</code></pre> </div> <h3>Implementation Examples</h3> <p>Here are practical examples showing how to implement JWT authentication in different programming languages and tools:</p> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🌐 cURL Example:</strong></p> <pre><code>curl -X GET "https://yoursite.axero.com/api/users/me" \\ -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \\ -H "Content-Type: application/json"</code></pre> </div> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🟨 JavaScript Example:</strong></p> <pre><code>fetch('https://yoursite.axero.com/api/users/me', { method: 'GET', headers: { 'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE', 'Content-Type': 'application/json' } }) .then(response =&gt; { if (!response.ok) { throw new Error(\`HTTP error! status: \${response.status}\`); } return response.json(); }) .then(data =&gt; console.log(data)) .catch(error =&gt; console.error('API Error:', error));</code></pre> </div> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🐍 Python Example:</strong></p> <pre><code>import requests headers = { 'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE', 'Content-Type': 'application/json' } try: response = requests.get('https://yoursite.axero.com/api/users/me', headers=headers) response.raise_for_status() # Raises an HTTPError for bad responses data = response.json() print(data) except requests.exceptions.RequestException as e: print(f"API Error: {e}")</code></pre> </div> <hr> <h2>API Usage Guidelines and Best Practices</h2> <p>Axero does not enforce hard rate limits on the REST API by default. However, to ensure consistent performance, reliability, and a seamless experience for all users, we recommend adhering to the best practices outlined below. These guidelines are intended to support high-performance, scalable integrations while preserving the responsiveness of your Axero environment. Please note that Axero may implement temporary rate restrictions if excessive API usage affects overall site performance.</p> <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; margin: 20px 0"> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 8px; padding: 20px; background: rgba(248, 249, 250, 1)"> <h4>⚡ Request Rate Guidelines</h4> <ul> <li><strong>Maximum Request Rate:</strong> Do not exceed <strong>5 API calls per second</strong> sustained</li> <li><strong>Concurrent Requests:</strong> Limit to <strong>3 or fewer concurrent connections</strong></li> <li><strong>Response Time Validation:</strong> Ensure API responses complete within <strong>15 seconds</strong> during testing</li> <li><strong>Sequential Processing:</strong> Wait for request completion before sending the next request in automated workflows</li> </ul> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 8px; padding: 20px; background: rgba(240, 255, 240, 1)"> <h4>🚀 Performance Optimization</h4> <ul> <li><strong>Implement Intelligent Caching:</strong> Store frequently accessed data locally with appropriate TTL (Time To Live)</li> <li><strong>Use Efficient Pagination:</strong> Retrieve large datasets in manageable chunks using pagination parameters</li> <li><strong>Apply Smart Filtering:</strong> Use query parameters to request only the specific data you need</li> <li><strong>Optimize Request Timing:</strong> Schedule bulk operations during off-peak hours when possible</li> </ul> </div> </div> <h3>Common HTTP Status Codes</h3> <div style="background: rgba(248, 249, 250, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <table style="width: 100%; border-collapse: collapse"> <tbody> <tr style="background: rgba(233, 236, 239, 1)"> <th style="padding: 10px; text-align: left; border: 1px solid rgba(222, 226, 230, 1)">Status Code</th> <th style="padding: 10px; text-align: left; border: 1px solid rgba(222, 226, 230, 1)">Meaning</th> <th style="padding: 10px; text-align: left; border: 1px solid rgba(222, 226, 230, 1)">Action</th> </tr> <tr> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)"><code>200</code></td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Success</td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Process the response data</td> </tr> <tr> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)"><code>401</code></td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Unauthorized</td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Check token validity, refresh if expired</td> </tr> <tr> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)"><code>403</code></td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Forbidden</td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">User lacks permissions for this action</td> </tr> <tr> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)"><code>429</code></td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Too Many Requests</td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Implement exponential backoff</td> </tr> <tr> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)"><code>500</code></td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Server Error</td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Retry after delay, contact support if persistent</td> </tr> </tbody> </table> </div> <hr> <h2 id="api-categories">API Categories</h2> <p>The Axero REST API is organized into several categories, each providing specific functionality for different aspects of your community platform. Each category includes comprehensive endpoints for creating, reading, updating, and managing data:</p> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(248, 249, 255, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-user" style="color: rgba(74, 144, 226, 1)"></em> Users API</h3> <p>Comprehensive user management capabilities for creating, updating, and managing user accounts and roles.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Retrieve user profiles and account information</li> <li>Create and manage user accounts</li> <li>Assign and update user roles and permissions</li> <li>Search and filter users by various criteria</li> <li>Manage user membership in spaces and groups</li> <li>Update user points and recognition metrics</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>User Synchronization:</strong> Import users from existing HR systems or databases</li> <li><strong>Automated Onboarding:</strong> Create user accounts when new employees join</li> <li><strong>Role Management:</strong> Update user roles when employees change positions</li> <li><strong>Directory Integration:</strong> Sync user information with Active Directory or LDAP</li> </ul> </div> </div> <div style="background: rgba(240, 248, 255, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/371/rest-api-users">REST API: Users</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(255, 248, 240, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-book" style="color: rgba(230, 126, 34, 1)"></em> Content API</h3> <p>Powerful content management tools for creating, retrieving, and managing all types of content within your community.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Create and publish articles, blog posts, and other content types</li> <li>Retrieve content lists with filtering and sorting options</li> <li>Manage content categories and tags</li> <li>Handle file uploads and attachments</li> <li>Access workflow and approval queues</li> <li>Manage content permissions and visibility</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Content Syndication:</strong> Display community content on external websites or digital signage</li> <li><strong>Automated Publishing:</strong> Create content from external systems or feeds</li> <li><strong>Content Migration:</strong> Import existing content from other platforms</li> <li><strong>Custom Dashboards:</strong> Build personalized content views and recommendations</li> </ul> </div> </div> <div style="background: rgba(255, 248, 240, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/374/rest-api-content">REST API: Content</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(240, 255, 248, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-sitemap" style="color: rgba(39, 174, 96, 1)"></em> Spaces API</h3> <p>Complete space management functionality for organizing your community into departments, teams, or project areas.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Create and configure new spaces</li> <li>Retrieve space information and metadata</li> <li>Manage space membership and access controls</li> <li>Assign and update space-specific roles</li> <li>Configure space settings and permissions</li> <li>Organize spaces in hierarchical structures</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Organizational Structure:</strong> Mirror your company's department structure in digital spaces</li> <li><strong>Project Management:</strong> Create temporary spaces for specific projects or initiatives</li> <li><strong>Access Control:</strong> Manage who can access sensitive or department-specific information</li> <li><strong>Automated Provisioning:</strong> Create spaces automatically based on external triggers</li> </ul> </div> </div> <div style="background: rgba(240, 255, 248, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/372/rest-api-spaces">REST API: Spaces</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(248, 240, 255, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-comment-alt" style="color: rgba(155, 89, 182, 1)"></em> Chat API</h3> <p>Real-time messaging capabilities for integrating chat functionality with external systems and workflows.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Send and receive chat messages</li> <li>Create and manage chat threads</li> <li>Add participants to conversations</li> <li>Retrieve message history and thread information</li> <li>Manage chat notifications and settings</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Workflow Integration:</strong> Trigger chat notifications from external systems</li> <li><strong>Customer Support:</strong> Route support requests to appropriate chat channels</li> <li><strong>Automated Messaging:</strong> Send system notifications or reminders via chat</li> <li><strong>Cross-Platform Communication:</strong> Bridge conversations between different systems</li> </ul> </div> </div> <div style="background: rgba(248, 240, 255, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/29840/rest-api-chat">REST API: Chat</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(255, 240, 248, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-comments" style="color: rgba(233, 30, 99, 1)"></em> Comments API</h3> <p>Engagement tools for managing comments and discussions across all content types in your community.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Create and post comments on content</li> <li>Retrieve comment threads and individual comments</li> <li>Edit and update existing comments</li> <li>Delete comments and manage moderation</li> <li>Set comment status and approval workflows</li> <li>Manage comment permissions and visibility</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Feedback Collection:</strong> Gather input and opinions on proposals or ideas</li> <li><strong>Moderation Automation:</strong> Implement automated comment filtering and approval</li> <li><strong>Engagement Analytics:</strong> Track discussion patterns and user engagement</li> <li><strong>Content Enhancement:</strong> Add contextual information or updates via comments</li> </ul> </div> </div> <div style="background: rgba(255, 240, 248, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/4844/rest-api-comments">REST API: Comments</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(240, 240, 240, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-lock" style="color: rgba(52, 73, 94, 1)"></em> Permissions API</h3> <p>Security and access control tools for managing user permissions and role-based access throughout your community.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Check user permissions for specific actions or content</li> <li>Update role permissions and access levels</li> <li>Validate access rights before performing operations</li> <li>Manage permission inheritance and overrides</li> <li>Audit permission changes and access patterns</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Access Validation:</strong> Verify user permissions before displaying content or features</li> <li><strong>Role Management:</strong> Programmatically update permissions when organizational roles change</li> <li><strong>Security Auditing:</strong> Track and report on permission usage and changes</li> <li><strong>Custom Authorization:</strong> Implement complex permission logic in external applications</li> </ul> </div> </div> <div style="background: rgba(240, 240, 240, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/25127/rest-api-permissions">REST API: Permissions</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(255, 250, 240, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-trophy" style="color: rgba(243, 156, 18, 1)"></em> Recognition API</h3> <p>Employee recognition and gamification tools for building engagement and celebrating achievements.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Award badges and recognition to users</li> <li>Retrieve recognition programs and available badges</li> <li>Access challenge and leaderboard data</li> <li>Get user-specific recognition insights and achievements</li> <li>Manage recognition workflows and approvals</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>External Integration:</strong> Award recognition when users complete tasks in other systems</li> <li><strong>Performance Tracking:</strong> Integrate recognition data with HR or performance management systems</li> <li><strong>Automated Recognition:</strong> Trigger awards based on specific behaviors or milestones</li> <li><strong>Gamification:</strong> Build custom leaderboards and achievement systems</li> </ul> </div> </div> <div style="background: rgba(255, 250, 240, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/81467/rest-api-recognition">REST API: Recognition</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(240, 255, 240, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="fas fa-analytics" style="color: rgba(22, 160, 133, 1)"></em> Analytics API</h3> <p>Comprehensive analytics and reporting capabilities for understanding community engagement and content performance.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Retrieve content engagement metrics and analytics</li> <li>Access user activity and participation data</li> <li>Get space-specific analytics and usage patterns</li> <li>Track trending content and popular topics</li> <li>Monitor community health and engagement metrics</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Custom Dashboards:</strong> Build executive dashboards with community metrics</li> <li><strong>Content Strategy:</strong> Identify trending topics and high-performing content</li> <li><strong>User Engagement:</strong> Track and reward the most active community members</li> <li><strong>ROI Reporting:</strong> Measure community impact and business value</li> </ul> </div> </div> <div style="background: rgba(240, 255, 240, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/85193/rest-api-analytics">REST API: Analytics</a>&nbsp;</div> </div> <h2>Getting Help</h2> <p>If you need assistance, <a href="https://my.axerosolutions.com/spaces/77/axero-online-support/cases/add-edit-case/0">open a support case</a> and include the following information:</p> <ul> <li><strong>API Endpoint:</strong> The specific endpoint and HTTP method (e.g., <code>GET /api/users/me</code>)</li> <li><strong>Request Headers:</strong> Include all headers, but redact your actual token</li> <li><strong>Request Body:</strong> Include the request payload if applicable</li> <li><strong>Error Response:</strong> Complete error message with HTTP status code</li> <li><strong>Network Environment:</strong> Any firewalls, proxies, or network restrictions</li> </ul>`,
    };

    return pages[pageName] || '<h1>Page Not Found</h1><p>The requested page could not be found.</p>';
  }

  generatePageOutline() {
    const content = document.getElementById('content');
    const outlineNav = document.getElementById('outlineNav');
    const headings = content.querySelectorAll('h1, h2, h3');

    if (headings.length === 0) {
      outlineNav.innerHTML = '<p class="no-headings">No headings found</p>';
      return;
    }

    let outlineHTML = '';
    headings.forEach((heading) => {
      const level = heading.tagName.toLowerCase();
      const text = heading.textContent;
      const id = heading.id || this.generateId(text);

      if (!heading.id) {
        heading.id = id;
      }

      const className = level === 'h3' ? 'outline-h3' : '';
      outlineHTML += `<a href="#${id}" class="outline-link ${className}">${text}</a>`;
    });

    outlineNav.innerHTML = outlineHTML;

    // Add click handlers for smooth scrolling
    outlineNav.querySelectorAll('a').forEach((link) => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          targetElement.scrollIntoView({ behavior: 'smooth' });
        }
      });
    });

    // Setup tabs functionality
    this.setupTabs();
  }

  setupTabs() {
    document.querySelectorAll('.tab-button').forEach((button) => {
      button.addEventListener('click', () => {
        const tabId = button.dataset.tab;
        const tabList = button.parentElement;
        const tabsContainer = tabList.parentElement;

        // Remove active class from all buttons and contents
        tabList.querySelectorAll('.tab-button').forEach((btn) => btn.classList.remove('active'));
        tabsContainer.querySelectorAll('.tab-content').forEach((content) => content.classList.remove('active'));

        // Add active class to clicked button and corresponding content
        button.classList.add('active');
        document.getElementById(tabId)?.classList.add('active');
      });
    });
  }

  updateActiveOutlineLink() {
    const headings = document.querySelectorAll('#content h1, #content h2, #content h3');
    const outlineLinks = document.querySelectorAll('.outline-link');

    let activeHeading = null;
    const scrollPosition = window.scrollY + 100;

    headings.forEach((heading) => {
      if (heading.offsetTop <= scrollPosition) {
        activeHeading = heading;
      }
    });

    outlineLinks.forEach((link) => {
      link.classList.remove('active');
      if (activeHeading && link.getAttribute('href') === `#${activeHeading.id}`) {
        link.classList.add('active');
      }
    });
  }

  generateId(text) {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();
  }

  getNavigationStructure() {
    return {
      'api-endpoints': {
        title: 'API Endpoints',
        icon: 'fas fa-code',
        pages: [],
      },
      'examples-tutorials': {
        title: 'Examples & Tutorials',
        icon: 'fas fa-book',
        pages: [],
      },
      authentication: {
        title: 'Authentication',
        icon: 'fas fa-shield-alt',
        pages: [],
      },
      configuration: {
        title: 'Configuration',
        icon: 'fas fa-cog',
        pages: [],
      },
      troubleshooting: {
        title: 'Troubleshooting',
        icon: 'fas fa-wrench',
        pages: [],
      },
      changelog: {
        title: 'Changelog',
        icon: 'fas fa-history',
        pages: [],
      },
    };
  }

  getPageIndex() {
    return {};
  }

  handleInitialRoute() {
    // Handle initial page load based on URL hash
    const hash = window.location.hash.substring(1);
    if (hash && this.getPageContent(hash) !== '<h1>Page Not Found</h1><p>The requested page could not be found.</p>') {
      this.currentPage = hash;
      // Find and activate the corresponding nav link
      const navLink = document.querySelector(`[data-page="${hash}"]`);
      if (navLink) {
        this.updateActiveNavigation(navLink);
      }
    }

    // Listen for hash changes
    window.addEventListener('hashchange', () => {
      const newHash = window.location.hash.substring(1);
      if (newHash && this.getPageContent(newHash) !== '<h1>Page Not Found</h1><p>The requested page could not be found.</p>') {
        this.navigateToPage(newHash);
        const navLink = document.querySelector(`[data-page="${newHash}"]`);
        if (navLink) {
          this.updateActiveNavigation(navLink);
        }
      }
    });
  }

  generateDynamicNavigation() {
    const config = this.getNavigationConfig();
    const pageIndex = this.getPageIndex();
    let navHTML = '';

    config.categories.forEach((category) => {
      if (category.type === 'single') {
        // Single page navigation item
        const page = category.pages[0];
        const pageData = pageIndex[page] || { title: page };
        const displayTitle = pageData.title || category.title;
        navHTML += `
          <li class="nav-item">
            <a href="#${page}" class="nav-link ${page === 'introduction' ? 'active' : ''}" data-page="${page}">
              <i class="${category.icon} nav-icon"></i>
              ${displayTitle}
            </a>
          </li>
        `;
      } else {
        // Category with subcategories
        const landingPage = this.getCategoryLandingPage(category.id);
        navHTML += `
          <li class="nav-item has-children">
            <button class="nav-toggle" data-page="${landingPage}" data-category="${category.id}">
              <i class="${category.icon} nav-icon"></i>
              <span class="nav-title">${category.title}</span>
              <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
            </button>
            <ul class="nav-children">
        `;

        category.subcategories.forEach((subcategory) => {
          navHTML += `
            <li class="nav-subsection">
              <span class="nav-subsection-title">${subcategory.title}</span>
              <ul class="nav-subsection-items">
          `;

          subcategory.pages.forEach((pageId) => {
            const pageData = pageIndex[pageId] || { title: pageId };
            navHTML += `
              <li><a href="#${pageId}" class="nav-link" data-page="${pageId}">${pageData.title}</a></li>
            `;
          });

          navHTML += `
              </ul>
            </li>
          `;
        });

        navHTML += `
            </ul>
          </li>
        `;
      }
    });

    return navHTML;
  }

  initializeNavigation() {
    // Generate dynamic navigation
    const navTree = document.querySelector('.nav-tree');
    if (navTree) {
      // Clear any existing content first
      navTree.innerHTML = '';

      // Create the correct navigation structure manually to ensure it works
      const correctNavHTML = `
        <li class="nav-item">
          <a href="#introduction" class="nav-link active" data-page="introduction">
            <i class="fas fa-home nav-icon"></i>
            Introduction
          </a>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="enabling-api-access">
            <i class="fas fa-cog nav-icon"></i>
            <span class="nav-title">Setup & Configuration</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">API Setup</span>
              <ul class="nav-subsection-items">
                <li><a href="#enabling-api-access" class="nav-link" data-page="enabling-api-access">Enabling API Access</a></li>
                <li><a href="#creating-bearer-tokens" class="nav-link" data-page="creating-bearer-tokens">Creating Bearer Tokens</a></li>
              </ul>
            </li>
            <li class="nav-subsection">
              <span class="nav-subsection-title">Email Settings</span>
              <ul class="nav-subsection-items">
                <li><a href="#how-to-enable-images-in-emails" class="nav-link" data-page="how-to-enable-images-in-emails">How to Enable Images in Emails</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="saml-2-0-sso">
            <i class="fas fa-shield-alt nav-icon"></i>
            <span class="nav-title">Authentication & Security</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">SSO Authentication</span>
              <ul class="nav-subsection-items">
                <li><a href="#saml-2-0-sso" class="nav-link" data-page="saml-2-0-sso">SAML 2.0 SSO</a></li>
                <li><a href="#setup-guide-onelogin-saml-sso" class="nav-link" data-page="setup-guide-onelogin-saml-sso">Setup Guide: OneLogin SAML SSO</a></li>
                <li><a href="#transitioning-to-jwt-authentication" class="nav-link" data-page="transitioning-to-jwt-authentication">Transitioning to JWT Authentication</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="journeys">
            <i class="fas fa-graduation-cap nav-icon"></i>
            <span class="nav-title">Tutorials & Guides</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Content Management</span>
              <ul class="nav-subsection-items">
                <li><a href="#create-wiki-page" class="nav-link" data-page="create-wiki-page">Create Wiki Page</a></li>
                <li><a href="#file-field" class="nav-link" data-page="file-field">File Field</a></li>
              </ul>
            </li>
            <li class="nav-subsection">
              <span class="nav-subsection-title">Journey Management</span>
              <ul class="nav-subsection-items">
                <li><a href="#journeys" class="nav-link" data-page="journeys">Journeys Overview</a></li>
                <li><a href="#create-journey" class="nav-link" data-page="create-journey">Create Journey</a></li>
                <li><a href="#journey-lifecycle-management" class="nav-link" data-page="journey-lifecycle-management">Journey Lifecycle Management</a></li>
                <li><a href="#managing-journey-participants" class="nav-link" data-page="managing-journey-participants">Managing Journey Participants</a></li>
                <li><a href="#navigating-a-journey-as-a-user" class="nav-link" data-page="navigating-a-journey-as-a-user">Navigating a Journey as a User</a></li>
              </ul>
            </li>
            <li class="nav-subsection">
              <span class="nav-subsection-title">Integration</span>
              <ul class="nav-subsection-items">
                <li><a href="#webhooks" class="nav-link" data-page="webhooks">Webhooks</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="using-the-rest-api">
            <i class="fas fa-code nav-icon"></i>
            <span class="nav-title">API Reference</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Getting Started</span>
              <ul class="nav-subsection-items">
                <li><a href="#using-the-rest-api" class="nav-link" data-page="using-the-rest-api">Using the REST API</a></li>
              </ul>
            </li>
            <li class="nav-subsection">
              <span class="nav-subsection-title">URL Management</span>
              <ul class="nav-subsection-items">
                <li><a href="#rest-api-add-url-mapping" class="nav-link" data-page="rest-api-add-url-mapping">Add URL Mapping</a></li>
                <li><a href="#rest-api-delete-url-mapping" class="nav-link" data-page="rest-api-delete-url-mapping">Delete URL Mapping</a></li>
                <li><a href="#rest-api-get-url-mapping" class="nav-link" data-page="rest-api-get-url-mapping">Get URL Mapping</a></li>
              </ul>
            </li>
            <li class="nav-subsection">
              <span class="nav-subsection-title">Analytics & Reporting</span>
              <ul class="nav-subsection-items">
                <li><a href="#rest-api-get-axero-copilot-conversation-records" class="nav-link" data-page="rest-api-get-axero-copilot-conversation-records">Get Copilot Conversation Records</a></li>
                <li><a href="#rest-api-get-search-activity-analytics" class="nav-link" data-page="rest-api-get-search-activity-analytics">Get Search Activity Analytics</a></li>
                <li><a href="#rest-api-get-search-content-analytics" class="nav-link" data-page="rest-api-get-search-content-analytics">Get Search Content Analytics</a></li>
              </ul>
            </li>
            <li class="nav-subsection">
              <span class="nav-subsection-title">System Operations</span>
              <ul class="nav-subsection-items">
                <li><a href="#rest-api-rebuild-index" class="nav-link" data-page="rest-api-rebuild-index">Rebuild Index</a></li>
                <li><a href="#rest-api-update-task" class="nav-link" data-page="rest-api-update-task">Update Task</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="troubleshooting-guide-for-blurry-images">
            <i class="fas fa-wrench nav-icon"></i>
            <span class="nav-title">Troubleshooting</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Common Issues</span>
              <ul class="nav-subsection-items">
                <li><a href="#troubleshooting-guide-for-blurry-images" class="nav-link" data-page="troubleshooting-guide-for-blurry-images">Troubleshooting Guide for Blurry Images</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="2022-enhancements">
            <i class="fas fa-history nav-icon"></i>
            <span class="nav-title">Changelog</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Version History</span>
              <ul class="nav-subsection-items">
                <li><a href="#2022-enhancements" class="nav-link" data-page="2022-enhancements">2022 Enhancements</a></li>
              </ul>
            </li>
          </ul>
        </li>
      `;

      navTree.innerHTML = correctNavHTML;

      // Force a small delay to ensure DOM is updated before attaching events
      setTimeout(() => {
        this.attachNavigationEvents();
      }, 100);
    }
  }

  forceCorrectNavigation() {
    console.log('Forcing correct navigation...');
    const navTree = document.querySelector('.nav-tree');
    if (navTree) {
      // Completely replace the navigation with the correct structure
      navTree.innerHTML = `
        <li class="nav-item">
          <a href="#introduction" class="nav-link active" data-page="introduction">
            <i class="fas fa-home nav-icon"></i>
            Introduction
          </a>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="enabling-api-access">
            <i class="fas fa-cog nav-icon"></i>
            <span class="nav-title">Setup & Configuration</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">API Setup</span>
              <ul class="nav-subsection-items">
                <li><a href="#enabling-api-access" class="nav-link" data-page="enabling-api-access">Enabling API Access</a></li>
                <li><a href="#creating-bearer-tokens" class="nav-link" data-page="creating-bearer-tokens">Creating Bearer Tokens</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="saml-2-0-sso">
            <i class="fas fa-shield-alt nav-icon"></i>
            <span class="nav-title">Authentication & Security</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">SSO Authentication</span>
              <ul class="nav-subsection-items">
                <li><a href="#saml-2-0-sso" class="nav-link" data-page="saml-2-0-sso">SAML 2.0 SSO</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="journeys">
            <i class="fas fa-graduation-cap nav-icon"></i>
            <span class="nav-title">Tutorials & Guides</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Journey Management</span>
              <ul class="nav-subsection-items">
                <li><a href="#journeys" class="nav-link" data-page="journeys">Journeys</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="using-the-rest-api">
            <i class="fas fa-code nav-icon"></i>
            <span class="nav-title">API Reference</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Getting Started</span>
              <ul class="nav-subsection-items">
                <li><a href="#using-the-rest-api" class="nav-link" data-page="using-the-rest-api">Using the REST API</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="troubleshooting-guide-for-blurry-images">
            <i class="fas fa-wrench nav-icon"></i>
            <span class="nav-title">Troubleshooting</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Common Issues</span>
              <ul class="nav-subsection-items">
                <li><a href="#troubleshooting-guide-for-blurry-images" class="nav-link" data-page="troubleshooting-guide-for-blurry-images">Troubleshooting Guide for Blurry Images</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="2022-enhancements">
            <i class="fas fa-history nav-icon"></i>
            <span class="nav-title">Changelog</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Version History</span>
              <ul class="nav-subsection-items">
                <li><a href="#2022-enhancements" class="nav-link" data-page="2022-enhancements">2022 Enhancements</a></li>
              </ul>
            </li>
          </ul>
        </li>
      `;

      console.log('Forced navigation HTML set. Final content:', navTree.innerHTML);

      // Attach events
      this.attachNavigationEvents();
    }
  }

  finalizeNavigation() {
    const navTree = document.querySelector('.nav-tree');
    if (navTree) {
      // Force a completely clean navigation
      navTree.innerHTML = `
        <li class="nav-item">
          <a href="#introduction" class="nav-link active" data-page="introduction">
            <i class="fas fa-home nav-icon"></i>
            Introduction
          </a>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="enabling-api-access">
            <i class="fas fa-cog nav-icon"></i>
            <span class="nav-title">Setup & Configuration</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">API Setup</span>
              <ul class="nav-subsection-items">
                <li><a href="#enabling-api-access" class="nav-link" data-page="enabling-api-access">Enabling API Access</a></li>
                <li><a href="#creating-bearer-tokens" class="nav-link" data-page="creating-bearer-tokens">Creating Bearer Tokens</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="saml-2-0-sso">
            <i class="fas fa-shield-alt nav-icon"></i>
            <span class="nav-title">Authentication & Security</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">SSO Authentication</span>
              <ul class="nav-subsection-items">
                <li><a href="#saml-2-0-sso" class="nav-link" data-page="saml-2-0-sso">SAML 2.0 SSO</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="journeys">
            <i class="fas fa-graduation-cap nav-icon"></i>
            <span class="nav-title">Tutorials & Guides</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Journey Management</span>
              <ul class="nav-subsection-items">
                <li><a href="#journeys" class="nav-link" data-page="journeys">Journeys</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="using-the-rest-api">
            <i class="fas fa-code nav-icon"></i>
            <span class="nav-title">API Reference</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Getting Started</span>
              <ul class="nav-subsection-items">
                <li><a href="#using-the-rest-api" class="nav-link" data-page="using-the-rest-api">Using the REST API</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="troubleshooting-guide-for-blurry-images">
            <i class="fas fa-wrench nav-icon"></i>
            <span class="nav-title">Troubleshooting</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Common Issues</span>
              <ul class="nav-subsection-items">
                <li><a href="#troubleshooting-guide-for-blurry-images" class="nav-link" data-page="troubleshooting-guide-for-blurry-images">Troubleshooting Guide for Blurry Images</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li class="nav-item has-children">
          <button class="nav-toggle" data-page="2022-enhancements">
            <i class="fas fa-history nav-icon"></i>
            <span class="nav-title">Changelog</span>
            <span class="toggle-icon"><i class="fas fa-chevron-right"></i></span>
          </button>
          <ul class="nav-children">
            <li class="nav-subsection">
              <span class="nav-subsection-title">Version History</span>
              <ul class="nav-subsection-items">
                <li><a href="#2022-enhancements" class="nav-link" data-page="2022-enhancements">2022 Enhancements</a></li>
              </ul>
            </li>
          </ul>
        </li>
      `;

      // Attach events to the new navigation
      this.attachNavigationEvents();
    }
  }

  attachNavigationEvents() {
    // Enhanced navigation initialization for individual pages
    const navLinks = document.querySelectorAll('.nav-link');

    // Handle navigation clicks for regular nav-links
    navLinks.forEach((link) => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const pageName = link.getAttribute('data-page');
        if (pageName) {
          this.navigateToPage(pageName);
          this.updateActiveNavigation(link);
        }
      });
    });

    // Handle toggle buttons for categories (both navigation and expanding/collapsing)
    const navToggles = document.querySelectorAll('.has-children > .nav-toggle');
    navToggles.forEach((toggle) => {
      toggle.addEventListener('click', (e) => {
        e.preventDefault();

        const navItem = toggle.parentElement;
        const children = navItem.querySelector('.nav-children');
        const icon = toggle.querySelector('.toggle-icon i');
        const pageName = toggle.getAttribute('data-page');

        // Handle navigation if there's a data-page attribute
        if (pageName) {
          this.navigateToPage(pageName);
          this.updateActiveNavigation(toggle);
        }

        // Handle expand/collapse
        if (children) {
          const isExpanded = children.classList.contains('expanded');
          children.classList.toggle('expanded');
          if (icon) {
            icon.className = isExpanded ? 'fas fa-chevron-right' : 'fas fa-chevron-down';
          }
          navItem.classList.toggle('expanded', !isExpanded);
        }
      });
    });
  }

  navigateToPage(pageName) {
    // Update URL hash
    window.location.hash = pageName;

    // Load page content
    const content = this.getPageContent(pageName);
    const contentArea = document.getElementById('content');
    if (contentArea) {
      contentArea.innerHTML = content;

      // Update page outline
      this.updatePageOutline();

      // Scroll to top
      contentArea.scrollTop = 0;

      // Update search index if needed
      this.updateSearchIndex();
    }
  }

  updateActiveNavigation(activeLink) {
    // Remove active class from all nav links and toggles
    document.querySelectorAll('.nav-link, .nav-toggle').forEach((link) => {
      link.classList.remove('active');
    });

    // Add active class to current link
    activeLink.classList.add('active');

    // Expand parent category if needed
    const parent = activeLink.closest('.has-children');
    if (parent) {
      const children = parent.querySelector('.nav-children');
      const toggle = parent.querySelector('.nav-toggle');

      if (children && !children.classList.contains('expanded')) {
        children.classList.add('expanded');
        const icon = toggle?.querySelector('.toggle-icon i');
        if (icon) {
          icon.className = 'fas fa-chevron-down';
        }
        parent.classList.add('expanded');
      }
    }
  }
  getNavigationStructure() {
    return {
      'api-endpoints': {
        title: 'API Endpoints',
        icon: 'fas fa-code',
        pages: [
          {
            id: 107229,
            title: 'REST API: Add URL Mapping',
            slug: 'rest-api-add-url-mapping',
            category: 'endpoints',
            content: '<h1 id="rest-api-add-url-mapping">REST API: Add URL Mapping</h1>\n<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title"><span>Add URL Mapping</span></h1> <h3 class="method-url"><span>POST /api/urlmapper</span></h3> <p><span>Create a custom URL mapping to redirect one URL to another for improved navigation and accessibility. Valid for Axero version 8.0 and above.</span></p> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP&nbsp;Method</span></div> <div class="span2"><span class="label label-warning type">POST</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc"><span>Product Version</span></span></div> <div class="span2"><span class="type">8.0 and above</span></div> </div> </div> </div> <table class="table table-striped"> <thead> <tr> <th class="name">Required Parameters</th> <th class="use">How to use</th> <th class="desc">Description</th> </tr> </thead> <tbody> <tr> <td>API Key</td> <td><span>Used in the request </span><span>URL:</span><br><span>&amp;token=x</span><br><span>OR</span><br><span>Set in header:</span><br><span>request.Headers.Add(Rest-Api-Key, x)</span></td> <td>Axero REST API key for the Axero portal you are making the call for.</td> </tr> <tr> <td>fromURL</td> <td>Used in the request URL as a query parameter</td> <td>The URL you want to redirect from</td> </tr> <tr> <td>toURL</td> <td>Used in the request URL as a query parameter</td> <td>The URL you want to redirect to</td> </tr> </tbody> </table> <h3>Example Request&nbsp;</h3> <p><span style="font-size: 12pt">POST</span>&nbsp; <a href="https://your-community.com/api/urlmapper?fromURL=https://your-community.com/people/20164&amp;toURL=https://your-community.com/spaces/11165">https://your-community.com/api/urlmapper?fromURL=https://your-community.com/people/20164&amp;toURL=https://your-community.com/spaces/11165</a></p> <h3>Example Response</h3> <p>True/false&nbsp;is returned.</p> <pre><code class="json">true</code></pre> <p>&nbsp;</p> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107229/rest-api-add-url-mapping',
            author: 'Vladana Garic',
            created: '2025-02-05T13:18:51.117',
            modified: '2025-02-05T13:19:00.257',
          },
          {
            id: 107230,
            title: 'REST API: Delete URL Mapping',
            slug: 'rest-api-delete-url-mapping',
            category: 'endpoints',
            content: '<h1 id="rest-api-delete-url-mapping">REST API: Delete URL Mapping</h1>\n<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Delete URL Mapping</h1> <h3 class="method-url">DELETE /api/urlmapper/multiple</h3> <p>Remove one or multiple custom URL redirections from the system. Valid for Axero version 8.0 and above.</p> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP&nbsp;Method</span></div> <div class="span2"><span class="label label-warning type">DELETE</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Product Version</span></div> <div class="span2"><span class="type">8.0 and above</span></div> </div> </div> </div> <table class="table table-striped" style="background-color: rgba(255, 255, 255, 1); height: 180px"> <thead> <tr style="height: 40px"> <th class="name" style="height: 40px; width: 108.062px">Required Parameters</th> <th class="use" style="height: 40px; width: 223.297px">How to use</th> <th class="desc" style="height: 40px; width: 297.641px">Description</th> </tr> </thead> <tbody> <tr style="height: 120px"> <td style="height: 120px; width: 108.062px">API Key</td> <td style="height: 120px; width: 223.297px">Used in the request URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td style="height: 120px; width: 297.641px">Axero REST API key for the Axero portal you are making the call for.</td> </tr> <tr style="height: 20px"> <td style="height: 20px; width: 108.062px">toURL</td> <td style="height: 20px; width: 223.297px">Used in the request URL as a query parameter</td> <td style="height: 20px; width: 297.641px">The URL you want to redirect to</td> </tr> </tbody> </table> <table class="table table-striped" style="background-color: rgba(255, 255, 255, 1); width: 672px"> <thead> <tr style="height: 40px"> <th class="name" style="height: 40px; width: 105.172px">Optional Parameters</th> <th class="use" style="height: 40px; width: 224.312px">How to use</th> <th class="desc" style="height: 40px; width: 299.516px">Description</th> </tr> </thead> <tbody> <tr style="height: 22px"> <td style="width: 105.172px; height: 22px">fromURL</td> <td style="width: 224.312px; height: 22px">Used in the request URL as a query parameter</td> <td style="width: 299.516px; height: 22px">The URL you want to redirect from</td> </tr> </tbody> </table> <p class="method-url"><strong>NOTE:</strong> If "fromURL" is provided, the API will delete all mappings pointing from that URL.</p> <h3>Example Request&nbsp;</h3> <p><span style="font-size: 12pt">DELETE</span>&nbsp; <a href="https://your-community.com/api/urlmapper?toURL=https://your-community.com/spaces/11165">https://your-community.com/api/urlmapper?toURL=https://your-community.com/spaces/11165</a>&nbsp;</p> <h3>Example Response</h3> <p>True/false&nbsp;is returned.</p> <pre><code class="json">true</code></pre> <p>&nbsp;</p> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> <p>See also: <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22482/url-mapper">URL&nbsp;Mapper</a></p> </div> </div>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107230/rest-api-delete-url-mapping',
            author: 'Vladana Garic',
            created: '2025-02-05T13:20:59.483',
            modified: '2025-02-05T13:32:08.69',
          },
          {
            id: 107231,
            title: 'REST API: Get Axero Copilot Conversation Records',
            slug: 'rest-api-get-axero-copilot-conversation-records',
            category: 'endpoints',
            content: '<h1 id="rest-api-get-axero-copilot-conversation-records">REST API: Get Axero Copilot Conversation Records</h1>\n<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Get Axero Copilot Conversation Records</h1> <h3 class="method-url">GET /api/analytics/copilot/conversations</h3> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <p class="method-desc">Retrieves conversation records and interaction details from Axero Copilot analytics, including conversation IDs, user IDs, timestamps, durations, questions, responses, and references.</p> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP&nbsp;Method</span></div> <div class="span2"><span class="label label-success type">GET</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Product Version</span></div> <div class="span2"><span class="type">8.x&nbsp;</span></div> </div> </div> </div> <table class="table table-striped"> <thead> <tr> <th class="name">Required Parameters</th> <th class="use">How to use</th> <th class="desc">Description</th> </tr> </thead> <tbody> <tr> <td>API Key</td> <td><span> Used in the request </span>URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td>Axero REST API key for the Axero portal you are making the call for.</td> </tr> </tbody> </table> <table class="table table-striped" style="width: 100%; height: 147px"> <thead> <tr style="height: 49px"> <th class="name" style="width: 22.5076%">Optional Parameters</th> <th class="use" style="width: 39.4285%">How to use</th> <th class="desc" style="width: 38.064%">Description</th> </tr> </thead> <tbody> <tr style="height: 49px"> <td style="width: 22.5076%">authorID</td> <td style="width: 39.4285%">&amp;authorID=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">The ID of the author to retrieve interactions from.</td> </tr> <tr style="height: 49px"> <td style="width: 22.5076%">question</td> <td style="width: 39.4285%">&amp;question=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">A keyword to search for in the interaction question text.</td> </tr> <tr style="height: 49px"> <td style="width: 22.5076%">feedback</td> <td style="width: 39.4285%">&amp;feedback=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">Filters interactions based on feedback type: <ul> <li><code>none</code> – No feedback provided.</li> <li><code>any</code> – Any feedback, regardless of type.</li> <li><code>positive</code> – Only interactions with positive feedback.</li> <li><code>negative</code> – Only interactions with negative feedback.</li> </ul> <p>NOTE: If not specified, all results will be included.</p> </td> </tr> <tr style="height: 49px"> <td style="width: 22.5076%">fromDate</td> <td style="width: 39.4285%">&amp;fromDate=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">Specifies the start date and time for filtering interactions, using the ISO format (YYYY-MM-DDTHH:mm:ss).</td> </tr> <tr style="height: 49px"> <td style="width: 22.5076%">toDate</td> <td style="width: 39.4285%">&amp;toDate=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">Specifies the end date and time for filtering interactions, using the ISO format (YYYY-MM-DDTHH:mm:ss).</td> </tr> <tr style="height: 49px"> <td style="width: 22.5076%">continuationToken</td> <td style="width: 39.4285%">&amp;continuationToken=x - Used in the request URL as a query parameter.</td> <td style="width: 38.064%">A pagination token for retrieving more results when additional data is available. If included in the response, use the token in the next request to fetch the next set of records.</td> </tr> </tbody> </table> <h3>Example Request</h3> <p class="method-url"><span style="font-size: 12pt">GET&nbsp;</span> <a href="#"> https://your-community.com/api/analytics/copilot/conversations?authorID=1245&amp;question=policies&amp;feedback=positive&amp;fromDate=2025-02-01T18:40:12.713115Z&amp;toDate=2025-02-03T18:40:12.713115Z </a></p> <h3>Example Response</h3> <pre><code class="json">{ "Duration": 0.0, "Interactions": [ { "Duration": 14.399983406066895, "DiscussionId": "d864415a-5b32-4f4a-93e6-a9b58e9e99c9", "InteractionId": "ed96cf9f-b17d-447f-a81a-ae526f4b1512", "Question": "What are the company policies?", "Response": "The company policies cover various aspects of employment and workplace conduct at Axero. Here is a summary of some key policies:\\n\\n1. **Company Handbook**: This document serves as a guide for the employer/employee relationship and contains general information and guidelines. It is not comprehensive and is subject to change. Employees should consult the Human Resource department for specific questions about benefits or policies.\\n\\n2. **Community and Charity**: This policy outlines restrictions for distributing materials and soliciting funds, donations, and signatures on company premises during working hours. It applies to all employees, external visitors, partners, and customers.\\n\\n3. **401(k) Plan Policy**: This policy provides a 401(k) Retirement Savings Plan to help employees accumulate financial resources for retirement. Eligibility, contributions, and terms of the plan are detailed within the policy.\\n\\n4. **Employee Training and Development Policy**: This policy refers to the company\'s learning and development programs and activities, encouraging continuous professional development for all permanent employees.\\n\\n5. **Smoke-Free Workplace**: Smoking is prohibited on all company premises to maintain a safe and healthy work environment for all employees. The policy applies to all areas of company buildings, company-sponsored off-site conferences and meetings, vehicles owned or leased by the company, visitors, contractors, consultants, and all employees.\\n\\nFor detailed information on each policy, employees should refer to the specific documents provided by the company. These policies may be modified or discontinued from time to time, and employees will be informed of any changes as they occur.\\n\\nFor more detailed information, you can access the documents through the following links:\\n- [Company Handbook](https://myintranet.com/spaces/252/human-resources/wiki/view/6708/company-handbook?language=en-US)\\n- [Community and Charity](https://myintranet.com/spaces/252/human-resources/wiki/view/8504/community-and-charity?language=en-US)\\n- [401(k) Plan Policy](https://myintranet.com/spaces/252/human-resources/wiki/view/8499/401-k-plan-policy?language=en-US)\\n- [Employee Training and Development Policy](https://myintranet.com/spaces/252/human-resources/wiki/view/8495/employee-training-and-development-policy?language=en-US)\\n- [Smoke-Free Workplace](https://myintranet.com/spaces/252/human-resources/wiki/view/6849/smoke-free-workplace?language=en-US)\\n\\nPlease note that these policies are specific to Acme Ltd. and may not apply to other organizations.", "Feedback": "helpful", "UserId": "1245", "UserName": "Alice Romero", "TimestampEnd": "1738671871.1736572", "TimestampStart": "1738671856.7736738", "DateEndISO": "2025-02-04T12:24:31.173657Z", "DateStartISO": "2025-02-04T12:24:16.773674Z", "DiscussionDateStartISO": "2025-02-04T12:24:16.773418Z", "References": [ { "ContentId": "6708", "EntityType": "9", "Relevance": "100", "SpaceId": "252", "Title": "Company Handbook", "URL": "https://myintranet.com/spaces/252/human-resources/wiki/view/6708/company-handbook?language=en-US" }, { "ContentId": "8504", "EntityType": "9", "Relevance": "100", "SpaceId": "252", "Title": "Community and Charity", "URL": "https://myintranet.com/spaces/252/human-resources/wiki/view/8504/community-and-charity?language=en-US" }, { "ContentId": "8499", "EntityType": "9", "Relevance": "100", "SpaceId": "252", "Title": "401(k) Plan Policy", "URL": "https://myintranet.com/spaces/252/human-resources/wiki/view/8499/401-k-plan-policy?language=en-US" }, { "ContentId": "8495", "EntityType": "9", "Relevance": "100", "SpaceId": "252", "Title": "Employee Training and Development Policy", "URL": "https://myintranet.com/spaces/252/human-resources/wiki/view/8495/employee-training-and-development-policy?language=en-US" }, { "ContentId": "6849", "EntityType": "9", "Relevance": "100", "SpaceId": "252", "Title": "Smoke-Free Workplace", "URL": "https://myintranet.com/spaces/252/human-resources/wiki/view/6849/smoke-free-workplace?language=en-US" } ], "UserFeedback": { "FeedbackIssues": [], "FeedbackText": "", "FeedbackType": "positive" } } ], "Temperature": 0.0, "ContinuationToken": "eyJ0b2RkYifX0=" } </code></pre> <br> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107231/rest-api-get-axero-copilot-conversation-records',
            author: 'Vladana Garic',
            created: '2025-02-05T13:45:42.157',
            modified: '2025-02-05T13:45:52.337',
          },
          {
            id: 106624,
            title: 'REST API: Get Search Activity Analytics',
            slug: 'rest-api-get-search-activity-analytics',
            category: 'endpoints',
            content: '<h1 id="rest-api-get-search-activity-analytics">REST API: Get Search Activity Analytics</h1>\n<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Get Search Activity Analytics</h1> <h3 class="method-url">GET api/search/activity</h3> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <p class="method-desc">Get a list of <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/26816/search-analytics" style="text-decoration: none">Search Activity Analytics</a>&nbsp;such as keywords, user details, timestamps, and engagement metrics.&nbsp;</p> <div class="row-fluid api-info"> <div class="span8 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">HTTP Method</span></div> <div class="span4"><span class="label label-info type">GET</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Response Format</span></div> <div class="span4"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Requires Authentication?</span></div> <div class="span4"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Product Version</span></div> <div class="span4"><span class="type">8.0 and above&nbsp;</span></div> </div> </div> </div> <table class="table table-striped" height="296" style="height: 296px; width: 700px; border-style: none"> <thead> <tr style="height: 40px"> <th class="name" style="height: 40px; width: 114.5px">Required Parameters</th> <th class="use" style="height: 40px; width: 241.547px">How to use</th> <th class="desc" style="height: 40px; width: 272.953px">Description</th> </tr> </thead> <tbody> <tr style="height: 120px"> <td style="height: 120px; width: 114.5px">API Key</td> <td style="height: 120px; width: 241.547px">Used in the request URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td style="height: 120px; width: 272.953px"> <p>Axero REST API key for the Axero portal you are making the call for.</p> </td> </tr> <tr> <td style="width: 114.5px">StartPage</td> <td style="width: 241.547px">&amp;StartPage=x - Used in the request URL (see below)</td> <td style="width: 272.953px">Specifies the starting page of list to be returned.</td> </tr> <tr> <td style="width: 114.5px">PageLength</td> <td style="width: 241.547px">&amp;PageLength=x - Used in the request URL (see below)</td> <td style="width: 272.953px">Specifies the number of results to be returned per page.</td> </tr> </tbody> </table> <table class="table table-striped" style="width: 663px; border-style: none"> <thead> <tr style="height: 40px"> <th class="name" style="height: 40px; width: 108.219px">Optional Parameters</th> <th class="use" style="height: 40px; width: 211.719px">How to use</th> <th class="desc" style="height: 40px; width: 309.062px">Description</th> </tr> </thead> <tbody> <tr style="height: 80px"> <td style="height: 80px; width: 108.219px">SpaceID</td> <td style="height: 80px; width: 211.719px">&amp;SpaceID=x - Used in the request URL</td> <td style="height: 80px; width: 309.062px">Unique ID of the space from which the content is to be returned.<br>Don\'t pass any value if you want content from every space, where you have access.</td> </tr> <tr style="height: 20px"> <td style="height: 20px; width: 108.219px">StartDate</td> <td style="height: 20px; width: 211.719px">&amp;startDate=x - Used in the request URL</td> <td style="height: 20px; width: 309.062px">Start date of range to Search history. The date format is YYYY-MM-DD:HH:mm:ssZ.&nbsp;</td> </tr> <tr style="height: 20px"> <td style="height: 20px; width: 108.219px">EndDate</td> <td style="height: 20px; width: 211.719px">&amp;endDate=x - Used in the request URL</td> <td style="height: 20px; width: 309.062px">End date of range to Search history. The date format is YYYY-MM-DD:HH:mm:ssZ.&nbsp;</td> </tr> <tr> <td style="width: 108.219px">SearchTerm</td> <td style="width: 211.719px">&amp;term=x - Used in the request URL</td> <td style="width: 309.062px">Specifies the search term to display results.&nbsp;</td> </tr> <tr> <td style="width: 108.219px">Person</td> <td style="width: 211.719px">&amp;UserID=x - Used in the request URL</td> <td style="width: 309.062px">User ID to return results for.&nbsp;</td> </tr> </tbody> </table> <p>&nbsp;</p> <h3>Example Request<br><a href="#"></a></h3> <p><span style="font-size: 14pt">GET</span> <a href="#">https://myintranet.communifire.com/api/search/activity?StartPage=1&amp;PageLength=2</a></p> <h3>Example Response</h3> <p>Content JSON object is returned as ResponseData.&nbsp;</p> <pre>{ "IsError": false, "ResponseMessage": "", "ResponseData": [ { "SearchTerm": "test", "UserID": 950, "UserDisplayName": "Alexis Fox", "UserProfileURL": "https://myintranet.communifire.com/people/<br> Alexis", "DateSearched": "2024-12-12T09:57:30.167", "DateSearchedString": "38 minutes ago", "ClickedEntityID": 0, "ClickedEntityType": 0, "SearchCount": 5, "ClickCount": 3, "ClickThroughRate": 0.8 }, { "SearchTerm": "marketing", "UserID": 943, "UserDisplayName": "Alice Romero", "UserProfileURL": "https://myintranet.communifire.com/people/<br> Alice", "DateSearched": "2024-12-12T09:57:24.51", "DateSearchedString": "50 minutes ago", "ClickedEntityID": 0, "ClickedEntityType": 0, "SearchCount": 3, "ClickCount": 1, "ClickThroughRate": 0.1 }<br>] }</pre> <p>&nbsp;</p> <h3>Example Request for searching with Search Term and Person<br><a href="#"></a></h3> <p><span style="font-size: 14pt">GET</span>&nbsp;<a href="#">https://myintranet.communifire.com/api/activityStartPage=1&amp;PageLength=2&amp;term=test&amp;UserID=950</a></p> <h3>Example Response</h3> <p>Content JSON object is returned as ResponseData.&nbsp;</p> <pre>{ "IsError": false, "ResponseMessage": "", "ResponseData": [ { "SearchTerm": "test", "UserID": 950, "UserDisplayName": "Alexis Fox", "UserProfileURL": "https://myintranet.communifire.com/people/<br> Alexis", "DateSearched": "2024-12-12T09:57:30.167", "DateSearchedString": "an hour ago", "ClickedEntityID": 0, "ClickedEntityType": 0, "SearchCount": 3, "ClickCount": 5, "ClickThroughRate": 0.8 }, { "SearchTerm": "testing", "UserID": 950, "UserDisplayName": "Alexis Fox", "UserProfileURL": "https://myintranet.communifire.com/people/<br> Alexis", "DateSearched": "2024-08-12T18:19:10.703", "DateSearchedString": "12/8/2024", "ClickedEntityID": 0, "ClickedEntityType": 0, "SearchCount": 8, "ClickCount": 1, "ClickThroughRate": 0.1 } ] }</pre> <br> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106624/rest-api-get-search-activity-analytics',
            author: 'Vladana Garic',
            created: '2024-12-16T17:09:51.083',
            modified: '2025-02-06T03:33:56.38',
          },
          {
            id: 106625,
            title: 'REST API: Get Search Content Analytics',
            slug: 'rest-api-get-search-content-analytics',
            category: 'endpoints',
            content: '<h1 id="rest-api-get-search-content-analytics">REST API: Get Search Content Analytics</h1>\n<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Get Search Content Analytics</h1> <h3 class="method-url">GET api/search/content</h3> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <p class="method-desc">Get a list of&nbsp;<a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/97007/analytics-search#content" style="text-decoration: none">Search Content Analytics</a>, such as search terms, content titles, and click counts for each entry.&nbsp;</p> <div class="row-fluid api-info"> <div class="span8 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">HTTP Method</span></div> <div class="span4"><span class="label label-info type">GET</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Response Format</span></div> <div class="span4"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Requires Authentication?</span></div> <div class="span4"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span8"><span class="desc">Product Version</span></div> <div class="span4"><span class="type">8.0 and above&nbsp; &nbsp;</span></div> </div> </div> </div> <table class="table table-striped" height="296" style="height: 193.717px; width: 653px; border-style: none"> <thead> <tr style="height: 48.2639px"> <th class="name" style="width: 148.464px">Required Parameters</th> <th class="use" style="width: 248.021px">How to use</th> <th class="desc" style="width: 224.288px">Description</th> </tr> </thead> <tbody> <tr style="height: 145.453px"> <td style="width: 148.464px">API Key</td> <td style="width: 248.021px">Used in the request URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td style="width: 224.288px">Axero REST API key for the Axero portal you are making the call for.</td> </tr> </tbody> </table> <table class="table table-striped" style="width: 663px; border-style: none; height: 645.139px"> <thead> <tr style="height: 48.2639px"> <th class="name" style="width: 108.219px">Optional Parameters</th> <th class="use" style="width: 211.719px">How to use</th> <th class="desc" style="width: 309.062px">Description</th> </tr> </thead> <tbody> <tr style="height: 68.5417px"> <td style="width: 114.5px">StartPage</td> <td style="width: 241.547px">&amp;StartPage=x - Used in the request URL (see below)</td> <td style="width: 272.953px">Specifies the starting page of list to be returned (defaults to 1 if not provided).</td> </tr> <tr style="height: 48.5417px"> <td style="width: 114.5px">PageLength</td> <td style="width: 241.547px">&amp;PageLength=x - Used in the request URL (see below)</td> <td style="width: 272.953px">Specifies the number of results to be returned per page. (defaults to 20)</td> </tr> <tr style="height: 108.542px"> <td style="width: 108.219px">SpaceID</td> <td style="width: 211.719px">&amp;SpaceID=x - Used in the request URL</td> <td style="width: 309.062px">Unique ID of the space from which the content is to be returned.<br>Don\'t pass any value if you want content from every space, where you have access.</td> </tr> <tr style="height: 68.5417px"> <td style="width: 108.219px">StartDate</td> <td style="width: 211.719px">&amp;startDate=x - Used in the request URL</td> <td style="width: 309.062px">Start date of range to Search history. The date format is YYYY-MM-DD:HH:mm:ssZ.&nbsp;</td> </tr> <tr style="height: 68.5417px"> <td style="width: 108.219px">EndDate</td> <td style="width: 211.719px">&amp;endDate=x - Used in the request URL</td> <td style="width: 309.062px">End date of range to Search history. The date format is YYYY-MM-DD:HH:mm:ssZ.&nbsp;</td> </tr> <tr style="height: 68.5417px"> <td style="width: 108.219px">SearchTerm</td> <td style="width: 211.719px">&amp;searchTerm=x - Used in the request URL</td> <td style="width: 309.062px"> <p>Specifies the search term to display results.&nbsp;</p> </td> </tr> <tr style="height: 48.5417px"> <td style="width: 108.219px">EntityTypeID</td> <td style="width: 211.719px">&amp;EntityTypeID=x - Used in the request URL</td> <td style="width: 309.062px">The entity type ID of the content to be returned. Here are the values of EntityTypeID parameter: <ul style="list-style-type: disc"> <li>Forum = 1</li> <li>ForumGroup = 2</li> <li>Article = 3</li> <li>Blog = 4</li> <li>Photo = 6</li> <li>Video = 7</li> <li>Wiki = 9</li> <li>CMSPage = 12</li> <li>File = 14</li> <li>Album = 18</li> <li>Idea = 44</li> <li>ForumPost = 54</li> <li>ForumTopic = 55</li> </ul> </td> </tr> <tr style="height: 48.5417px"> <td style="width: 108.219px">noOfSearchTermResult</td> <td style="width: 211.719px">&amp;noOfSearchTermResult=x - Used in the request URL</td> <td style="width: 309.062px">Specifies the maximum number of search terms to include for each piece of content (defaults to 20).</td> </tr> </tbody> </table> <p>&nbsp;</p> <h3>Example Request<br><a href="#"></a></h3> <p><span style="font-size: 12pt"><span style="font-size: 14pt">GET&nbsp;</span><a href="#">https://myintranet.communifire.com/api/search/content?SpaceID=252<br>&amp;PageLength=2&amp;EntityTypeID=3</a></span></p> <h3>Example Response</h3> <p>Content JSON object is returned as ResponseData.&nbsp;</p> <pre>[ { "ContentID": 6918, "EntityTypeID": 3, "ContentTypeID": 3, "ContentTypeText": "Article", "ContentTitle": "Open Enrollment Starts Today! Time to Choose Your <br> Health Plan for This Upcoming Year", "SpaceID": 252, "SpaceName": "Human Resources", "ClickCount": 25, "SearchTerms": "a,benefits,open enroll,open enrollment" }, { "ContentID": 6646, "EntityTypeID": 3, "ContentTypeID": 3, "ContentTypeText": "Article", "ContentTitle": "The Value of Processes and Procedures in the Work <br> place", "SpaceID": 252, "SpaceName": "Human Resources", "ClickCount": 32, "SearchTerms": "value,work" }<br>]</pre> <p>&nbsp;</p> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> <p>&nbsp;</p> </div> </div> </div> </div> </div>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106625/rest-api-get-search-content-analytics',
            author: 'Vladana Garic',
            created: '2024-12-16T17:12:39.913',
            modified: '2025-01-27T13:55:54.053',
          },
          {
            id: 107228,
            title: 'REST API: Get URL Mapping',
            slug: 'rest-api-get-url-mapping',
            category: 'endpoints',
            content: '<h1 id="rest-api-get-url-mapping">REST API: Get URL Mapping</h1>\n<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title"><span>Get URL Mapping</span></h1> <h3 class="method-url"><span>GET /api/urlmapper</span></h3> <p><span>Get URL mapping details. Valid for Axero version 8.0 and above.</span></p> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP&nbsp;Method</span></div> <div class="span2"><span class="label label-warning type">GET</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc"><span>Product Version</span></span></div> <div class="span2"><span class="type">8.0 and above</span></div> </div> </div> </div> <table class="table table-striped" style="width: 100%"> <thead> <tr> <th class="name" style="width: 17.3716%">Required Parameters</th> <th class="use" style="width: 33.9879%">How to use</th> <th class="desc" style="width: 48.6405%">Description</th> </tr> </thead> <tbody> <tr> <td style="width: 17.3716%">API Key</td> <td style="width: 33.9879%"><span>Used in the request </span><span>URL:</span><br><span>&amp;token=x</span><br><span>OR</span><br><span>Set in header:</span><br><span>request.Headers.Add(Rest-Api-Key, x)</span></td> <td style="width: 48.6405%">Axero REST API key for the Axero portal you are making the call for.</td> </tr> <tr> <td style="width: 17.3716%">URLID</td> <td style="width: 33.9879%">Used in the request URL as a query parameter</td> <td style="width: 48.6405%">ID of URL Mapper entry you want to get.</td> </tr> </tbody> </table> <h3>Example Request&nbsp;</h3> <p><span style="font-size: 12pt">GET</span>&nbsp; <a href="https://your-community.com/api/urlmapper?urlID=55">https://your-community.com/api/urlmapper?urlID=55</a></p> <h3><span style="font-size: 18pt">Example Response</span></h3> <pre><code class="json">{ "IsError": false, "ResponseMessage": "", "ResponseData":{ "UrlID": 55, "OldUrl": "https://your-community.com/spaces/291/training-and-development/video/admin/instructional/9457/05-top-level-community-and-spaces", "NewUrl": "https://your-community.com/spaces/291/training-and-development/videos/admin/instructional/9457/05-top-level-community-and-spaces", "RedirectTypeID": 1, "IsActive": true, "DateAdded": "2022-09-21T20:34:39.8" } } </code></pre> <br> <p><span class="label label-important">Please Note</span>&nbsp;The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107228/rest-api-get-url-mapping',
            author: 'Vladana Garic',
            created: '2025-02-05T13:16:08.797',
            modified: '2025-02-05T22:09:34.177',
          },
          {
            id: 106388,
            title: 'REST API: Rebuild Index',
            slug: 'rest-api-rebuild-index',
            category: 'endpoints',
            content: '<h1 id="rest-api-rebuild-index">REST API: Rebuild Index</h1>\n<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Rebuild index</h1> <h3 class="method-url">POST /api/admin/buildindex</h3> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <p class="method-desc">Rebuild the search index.&nbsp;</p> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP&nbsp;Method</span></div> <div class="span2"><span class="label label-success type">POST</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span><span class="desc"></span></div> </div> </div> </div> <table class="table table-striped" style="width: 100%; height: 194px"> <thead> <tr style="height: 40px"> <th class="name" style="width: 15.0983%; height: 40px">Required Parameters</th> <th class="use" style="width: 31.184%; height: 40px">How to use</th> <th class="desc" style="width: 48.5894%; height: 40px">Description</th> </tr> </thead> <tbody> <tr style="height: 154px"> <td style="width: 15.0983%; height: 154px">API Key</td> <td style="width: 31.184%; height: 154px">Used in the request URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td style="width: 48.5894%; height: 154px">Axero REST API key for the Axero portal you are making the call for.</td> </tr> <tr style="height: 80px"> <td style="width: 17.6737%; height: 80px">EntityTypeCSV</td> <td style="width: 32.1752%; height: 80px">Used in the request body&nbsp;</td> <td style="width: 50.1511%; height: 80px">A comma-separated string of EntityTypeIDs to rebuild. Check the list of Entity types in <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/25112/rest-api-system-entity-types">REST API: System Entity Types</a></strong>.</td> </tr> </tbody> </table> <table class="table table-striped" style="width: 100.302%; height: 195px"> <thead> <tr style="height: 40px"> <th class="name" style="width: 18.1269%; height: 40px">Optional Parameters</th> <th class="use" style="width: 34.7432%; height: 40px">How to use</th> <th class="desc" style="width: 47.1299%; height: 40px">Description</th> </tr> </thead> <tbody> <tr style="height: 80px"> <td style="width: 18.1269%; height: 80px">IsRebuildAll</td> <td style="width: 34.7432%; height: 80px">&amp;IsRebuildAll=true/false - Used in the request URL as a query parameter</td> <td style="width: 47.1299%; height: 80px"> <p>Set it to <em>true</em> to rebuild the entire index; ensure that&nbsp;<strong>all</strong> entity types are included in EntityTypeCSV for a complete index update.<br>Set it to <em>false</em> to reindex only the specified entity types included in the request body.</p> </td> </tr> </tbody> </table> <h3>Example Request</h3> <p><span style="font-size: 12pt">POST</span> <a href="#">https://myintranet.communifire.com/api/admin/buildindex?IsRebuildAll=false</a></p> <p>Body:</p> <pre><code class="json">[3, 4, 5]&nbsp;</code></pre> <h3>True response code is returned if successful</h3> <pre><code class="json">&nbsp;true</code></pre> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106388/rest-api-rebuild-index',
            author: 'Vladana Garic',
            created: '2024-11-28T16:25:22.85',
            modified: '2025-01-28T19:05:53.407',
          },
          {
            id: 107073,
            title: 'REST API: Update Task',
            slug: 'rest-api-update-task',
            category: 'endpoints',
            content: '<h1 id="rest-api-update-task">REST API: Update Task</h1>\n<div class="row-fluid rest-documentation-container"> <div class="span12"> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <h1 class="method-title">Update Task</h1> <h3 class="method-url">PUT /api/tasks/<span class="api-param">{TaskID}</span></h3> </div> </div> </div> <div class="row-fluid-wrapper"> <div class="row-fluid "> <div class="span12"> <p class="method-desc">Update a task.</p> <div class="row-fluid api-info"> <div class="span6 alert alert-block"> <div class="row-fluid"> <div class="span12"> <h3>Method Details</h3> </div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">HTTP Method</span></div> <div class="span2"><span class="label label-warning type">PUT</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Response Format</span></div> <div class="span2"><span class="type">JSON</span></div> </div> <div class="row-fluid margintop10"> <div class="span10"><span class="desc">Requires Authentication?</span></div> <div class="span2"><span class="type">YES</span></div> </div> </div> </div> <table class="table table-striped"> <thead> <tr> <th class="name">Required Parameters</th> <th class="use">How to use</th> <th class="desc">Description</th> </tr> </thead> <tbody> <tr> <td>API Key</td> <td>Used in the request URL:<br>&amp;token=x<br>OR<br>Set in header:<br>request.Headers.Add(Rest-Api-Key, x)</td> <td>Axero REST API key for the Axero portal you are making the call for.</td> </tr> <tr> <td>TaskID</td> <td>Used in the request URL as a path parameter</td> <td>Unique ID of the task.</td> </tr> <tr> <td>Task JSON</td> <td>Used in the request body</td> <td>JSON&nbsp;representing the task.</td> </tr> </tbody> </table> <table class="table"> <thead> <tr> <th class="name">Optional Parameters</th> <th class="use">How to use</th> <th class="desc">Description</th> </tr> </thead> <tbody> <tr> <td>TaskName</td> <td>Used in the request body</td> <td>The task name.</td> </tr> <tr> <td>TaskListID</td> <td>Used in the request body</td> <td>ID of the task list to add the task to.</td> </tr> <tr> <td>SpaceID</td> <td>Used in the request body</td> <td>ID of the space the task list is in.</td> </tr> <tr> <td>TaskDescription</td> <td>Used in the request body</td> <td>The task description.</td> </tr> <tr> <td>AssignedToUserID</td> <td>Used in the request body</td> <td>ID of the user to assign the task to.</td> </tr> <tr> <td>TaskStatusID</td> <td>Used in the request body</td> <td>ID of the task status to set.</td> </tr> <tr> <td>TaskPriorityID</td> <td>Used in the request body</td> <td>ID of the task priority to set.</td> </tr> <tr> <td>TaskDueDate</td> <td>Used in the request body</td> <td>The task due date.</td> </tr> <tr> <td>TaskEstimatedHours</td> <td>Used in the request body</td> <td>The task\'s estimated completion time in hours.</td> </tr> <tr> <td>FollowerUserNamesCsv</td> <td>Used in the request body</td> <td>A comma-separated list of usernames&nbsp;of users to add as task followers.</td> </tr> </tbody> </table> <h3>Example Request</h3> <p><span style="font-size: 14pt">PUT</span> <a href="#">https://myintranet.communifire.com/api/tasks/734</a></p> <p>Body:</p> <pre><code class="json"> { "TaskName": "Prepare Q4 Financial Report", "TaskListID": 704, "SpaceID": 418, "TaskDescription": "Compile and analyze financial data for the Q4 <br> report, ensuring accuracy and completeness.", "AssignedToUserID": 573, "TaskStatusID": 3729, "TaskPriorityID": 2470, "TaskDueDate": "12/20/2024", "TaskEstimatedHours": 5, "FollowerUserNamesCsv": "robert,alice" }</code></pre> <h3>Example Response</h3> <p>Task JSON object is returned as ResponseData.</p> <pre><code class="json">{ "IsError": false, "ResponseMessage": "Task has been updated.", "ResponseData": { "TaskID": 734, "TaskName": "Prepare Q4 Financial Report", "TaskDescription": "Compile and analyze financial data for the Q4 <br> report, ensuring accuracy and completeness.", "IssueDescriptionUnprocessed": "Compile and analyze financial data <br> for the Q4 report, ensuring accuracy and completeness.", "TaskSortOrder": 1, "TaskListID": 704, "IsCompleted": false, "SpaceID": 418, "AssignedToUserID": 573, "TaskDueDate": "2024-12-20T00:00:00", "DateStringTaskEnd": "20/12/2024", "PastDue": false, "AssignedToUserDisplayName": "Bethany Kim", "TaskListName": "Finance List ", "TaskStatusTypeID": 0, "TaskStatusID": 3729, "TaskPriorityID": 2470, "DateCreated": "2024-12-11T23:04:02.4", "ReportedByUserID": 950, "DateUpdated": "2024-12-11T23:28:11.4954293Z", "FollowerUserNamesCsv": "602,987", "ReferencedEntityID": 0, "ReferencedEntityType": 0, "TaskEstimatedHours": 5.0 } }</code></pre> <p>&nbsp;</p> <p><span class="label label-important">Please Note</span> The content type that you pass in the header of your request should be <strong>application/json</strong>.</p> </div> </div> </div> </div> </div>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107073/rest-api-update-task',
            author: 'Vladana Garic',
            created: '2025-01-22T23:08:43.287',
            modified: '2025-02-05T21:48:25.32',
          },
          {
            id: 108975,
            title: 'Using the REST API',
            slug: 'using-the-rest-api',
            category: 'endpoints',
            content: '<h1 id="using-the-rest-api">Using the REST API</h1>\n<p>This guide covers the practical implementation of the Axero REST API, including JWT Bearer Token authentication, performance optimization, and security best practices. Whether you\'re migrating from legacy API keys or building new integrations, this documentation provides the essential knowledge for successful API implementation.</p> <hr> <h2>Authentication with JWT Bearer Tokens</h2> <p>The Axero REST API uses <strong>JWT Bearer Tokens</strong> for secure authentication. These cryptographically signed tokens provide enhanced security compared to traditional API keys and follow industry standards.</p> <h3>Basic Authentication Format</h3> <p>After you <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108973/creating-bearer-tokens">create your JWT token</a>, include it in the <code>Authorization</code> header of all API requests:</p> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>📝 Request Format</strong></p> <pre>GET /api/users/me Host: yoursite.axero.com Authorization: Bearer YOUR_JWT_TOKEN_HERE Content-Type: application/json</pre> </div> <h3>Migrating from Legacy API Keys</h3> <p>The primary change involves updating the authorization header format in all your API requests:</p> <div style="background: rgba(255, 243, 205, 1); border: 1px solid rgba(255, 234, 167, 1); border-radius: 8px; padding: 15px"> <p><strong>❌ Legacy Format</strong></p> <pre><code>Authorization: rest-api-key YOUR_LEGACY_KEY_HERE</code></pre> </div> <div style="margin-top: 15px; background: rgba(212, 237, 218, 1); border: 1px solid rgba(195, 230, 203, 1); border-radius: 8px; padding: 15px"> <p><strong>✅ New JWT Format</strong></p> <pre><code>Authorization: Bearer YOUR_JWT_TOKEN_HERE</code></pre> </div> <h3>Implementation Examples</h3> <p>Here are practical examples showing how to implement JWT authentication in different programming languages and tools:</p> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🌐 cURL Example:</strong></p> <pre><code>curl -X GET "https://yoursite.axero.com/api/users/me" \\ -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \\ -H "Content-Type: application/json"</code></pre> </div> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🟨 JavaScript Example:</strong></p> <pre><code>fetch(\'https://yoursite.axero.com/api/users/me\', { method: \'GET\', headers: { \'Authorization\': \'Bearer YOUR_JWT_TOKEN_HERE\', \'Content-Type\': \'application/json\' } }) .then(response =&gt; { if (!response.ok) { throw new Error(`HTTP error! status: ${response.status}`); } return response.json(); }) .then(data =&gt; console.log(data)) .catch(error =&gt; console.error(\'API Error:\', error));</code></pre> </div> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🐍 Python Example:</strong></p> <pre><code>import requests headers = { \'Authorization\': \'Bearer YOUR_JWT_TOKEN_HERE\', \'Content-Type\': \'application/json\' } try: response = requests.get(\'https://yoursite.axero.com/api/users/me\', headers=headers) response.raise_for_status() # Raises an HTTPError for bad responses data = response.json() print(data) except requests.exceptions.RequestException as e: print(f"API Error: {e}")</code></pre> </div> <hr> <h2>API Usage Guidelines and Best Practices</h2> <p>Axero does not enforce hard rate limits on the REST API by default. However, to ensure consistent performance, reliability, and a seamless experience for all users, we recommend adhering to the best practices outlined below. These guidelines are intended to support high-performance, scalable integrations while preserving the responsiveness of your Axero environment. Please note that Axero may implement temporary rate restrictions if excessive API usage affects overall site performance.</p> <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; margin: 20px 0"> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 8px; padding: 20px; background: rgba(248, 249, 250, 1)"> <h4>⚡ Request Rate Guidelines</h4> <ul> <li><strong>Maximum Request Rate:</strong> Do not exceed <strong>5 API calls per second</strong> sustained</li> <li><strong>Concurrent Requests:</strong> Limit to <strong>3 or fewer concurrent connections</strong></li> <li><strong>Response Time Validation:</strong> Ensure API responses complete within <strong>15 seconds</strong> during testing</li> <li><strong>Sequential Processing:</strong> Wait for request completion before sending the next request in automated workflows</li> </ul> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 8px; padding: 20px; background: rgba(240, 255, 240, 1)"> <h4>🚀 Performance Optimization</h4> <ul> <li><strong>Implement Intelligent Caching:</strong> Store frequently accessed data locally with appropriate TTL (Time To Live)</li> <li><strong>Use Efficient Pagination:</strong> Retrieve large datasets in manageable chunks using pagination parameters</li> <li><strong>Apply Smart Filtering:</strong> Use query parameters to request only the specific data you need</li> <li><strong>Optimize Request Timing:</strong> Schedule bulk operations during off-peak hours when possible</li> </ul> </div> </div> <h3>Common HTTP Status Codes</h3> <div style="background: rgba(248, 249, 250, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <table style="width: 100%; border-collapse: collapse"> <tbody> <tr style="background: rgba(233, 236, 239, 1)"> <th style="padding: 10px; text-align: left; border: 1px solid rgba(222, 226, 230, 1)">Status Code</th> <th style="padding: 10px; text-align: left; border: 1px solid rgba(222, 226, 230, 1)">Meaning</th> <th style="padding: 10px; text-align: left; border: 1px solid rgba(222, 226, 230, 1)">Action</th> </tr> <tr> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)"><code>200</code></td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Success</td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Process the response data</td> </tr> <tr> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)"><code>401</code></td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Unauthorized</td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Check token validity, refresh if expired</td> </tr> <tr> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)"><code>403</code></td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Forbidden</td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">User lacks permissions for this action</td> </tr> <tr> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)"><code>429</code></td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Too Many Requests</td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Implement exponential backoff</td> </tr> <tr> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)"><code>500</code></td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Server Error</td> <td style="padding: 10px; border: 1px solid rgba(222, 226, 230, 1)">Retry after delay, contact support if persistent</td> </tr> </tbody> </table> </div> <hr> <h2 id="api-categories">API Categories</h2> <p>The Axero REST API is organized into several categories, each providing specific functionality for different aspects of your community platform. Each category includes comprehensive endpoints for creating, reading, updating, and managing data:</p> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(248, 249, 255, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-user" style="color: rgba(74, 144, 226, 1)"></em> Users API</h3> <p>Comprehensive user management capabilities for creating, updating, and managing user accounts and roles.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Retrieve user profiles and account information</li> <li>Create and manage user accounts</li> <li>Assign and update user roles and permissions</li> <li>Search and filter users by various criteria</li> <li>Manage user membership in spaces and groups</li> <li>Update user points and recognition metrics</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>User Synchronization:</strong> Import users from existing HR systems or databases</li> <li><strong>Automated Onboarding:</strong> Create user accounts when new employees join</li> <li><strong>Role Management:</strong> Update user roles when employees change positions</li> <li><strong>Directory Integration:</strong> Sync user information with Active Directory or LDAP</li> </ul> </div> </div> <div style="background: rgba(240, 248, 255, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/371/rest-api-users">REST API: Users</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(255, 248, 240, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-book" style="color: rgba(230, 126, 34, 1)"></em> Content API</h3> <p>Powerful content management tools for creating, retrieving, and managing all types of content within your community.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Create and publish articles, blog posts, and other content types</li> <li>Retrieve content lists with filtering and sorting options</li> <li>Manage content categories and tags</li> <li>Handle file uploads and attachments</li> <li>Access workflow and approval queues</li> <li>Manage content permissions and visibility</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Content Syndication:</strong> Display community content on external websites or digital signage</li> <li><strong>Automated Publishing:</strong> Create content from external systems or feeds</li> <li><strong>Content Migration:</strong> Import existing content from other platforms</li> <li><strong>Custom Dashboards:</strong> Build personalized content views and recommendations</li> </ul> </div> </div> <div style="background: rgba(255, 248, 240, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/374/rest-api-content">REST API: Content</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(240, 255, 248, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-sitemap" style="color: rgba(39, 174, 96, 1)"></em> Spaces API</h3> <p>Complete space management functionality for organizing your community into departments, teams, or project areas.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Create and configure new spaces</li> <li>Retrieve space information and metadata</li> <li>Manage space membership and access controls</li> <li>Assign and update space-specific roles</li> <li>Configure space settings and permissions</li> <li>Organize spaces in hierarchical structures</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Organizational Structure:</strong> Mirror your company\'s department structure in digital spaces</li> <li><strong>Project Management:</strong> Create temporary spaces for specific projects or initiatives</li> <li><strong>Access Control:</strong> Manage who can access sensitive or department-specific information</li> <li><strong>Automated Provisioning:</strong> Create spaces automatically based on external triggers</li> </ul> </div> </div> <div style="background: rgba(240, 255, 248, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/372/rest-api-spaces">REST API: Spaces</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(248, 240, 255, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-comment-alt" style="color: rgba(155, 89, 182, 1)"></em> Chat API</h3> <p>Real-time messaging capabilities for integrating chat functionality with external systems and workflows.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Send and receive chat messages</li> <li>Create and manage chat threads</li> <li>Add participants to conversations</li> <li>Retrieve message history and thread information</li> <li>Manage chat notifications and settings</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Workflow Integration:</strong> Trigger chat notifications from external systems</li> <li><strong>Customer Support:</strong> Route support requests to appropriate chat channels</li> <li><strong>Automated Messaging:</strong> Send system notifications or reminders via chat</li> <li><strong>Cross-Platform Communication:</strong> Bridge conversations between different systems</li> </ul> </div> </div> <div style="background: rgba(248, 240, 255, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/29840/rest-api-chat">REST API: Chat</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(255, 240, 248, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-comments" style="color: rgba(233, 30, 99, 1)"></em> Comments API</h3> <p>Engagement tools for managing comments and discussions across all content types in your community.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Create and post comments on content</li> <li>Retrieve comment threads and individual comments</li> <li>Edit and update existing comments</li> <li>Delete comments and manage moderation</li> <li>Set comment status and approval workflows</li> <li>Manage comment permissions and visibility</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Feedback Collection:</strong> Gather input and opinions on proposals or ideas</li> <li><strong>Moderation Automation:</strong> Implement automated comment filtering and approval</li> <li><strong>Engagement Analytics:</strong> Track discussion patterns and user engagement</li> <li><strong>Content Enhancement:</strong> Add contextual information or updates via comments</li> </ul> </div> </div> <div style="background: rgba(255, 240, 248, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/4844/rest-api-comments">REST API: Comments</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(240, 240, 240, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-lock" style="color: rgba(52, 73, 94, 1)"></em> Permissions API</h3> <p>Security and access control tools for managing user permissions and role-based access throughout your community.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Check user permissions for specific actions or content</li> <li>Update role permissions and access levels</li> <li>Validate access rights before performing operations</li> <li>Manage permission inheritance and overrides</li> <li>Audit permission changes and access patterns</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Access Validation:</strong> Verify user permissions before displaying content or features</li> <li><strong>Role Management:</strong> Programmatically update permissions when organizational roles change</li> <li><strong>Security Auditing:</strong> Track and report on permission usage and changes</li> <li><strong>Custom Authorization:</strong> Implement complex permission logic in external applications</li> </ul> </div> </div> <div style="background: rgba(240, 240, 240, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/25127/rest-api-permissions">REST API: Permissions</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(255, 250, 240, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="icon-trophy" style="color: rgba(243, 156, 18, 1)"></em> Recognition API</h3> <p>Employee recognition and gamification tools for building engagement and celebrating achievements.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Award badges and recognition to users</li> <li>Retrieve recognition programs and available badges</li> <li>Access challenge and leaderboard data</li> <li>Get user-specific recognition insights and achievements</li> <li>Manage recognition workflows and approvals</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>External Integration:</strong> Award recognition when users complete tasks in other systems</li> <li><strong>Performance Tracking:</strong> Integrate recognition data with HR or performance management systems</li> <li><strong>Automated Recognition:</strong> Trigger awards based on specific behaviors or milestones</li> <li><strong>Gamification:</strong> Build custom leaderboards and achievement systems</li> </ul> </div> </div> <div style="background: rgba(255, 250, 240, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/81467/rest-api-recognition">REST API: Recognition</a>&nbsp;</div> </div> <div style="border: 1px solid rgba(224, 224, 224, 1); border-radius: 12px; padding: 25px; margin: 20px 0; background: linear-gradient(135deg, rgba(240, 255, 240, 1) 0, rgba(255, 255, 255, 1) 100%)"> <h3><em class="fas fa-analytics" style="color: rgba(22, 160, 133, 1)"></em> Analytics API</h3> <p>Comprehensive analytics and reporting capabilities for understanding community engagement and content performance.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">🔧 Key Features</h4> <ul style="margin: 0; padding-left: 20px"> <li>Retrieve content engagement metrics and analytics</li> <li>Access user activity and participation data</li> <li>Get space-specific analytics and usage patterns</li> <li>Track trending content and popular topics</li> <li>Monitor community health and engagement metrics</li> </ul> </div> <div> <h4 style="color: rgba(44, 62, 80, 1); margin-bottom: 10px">💼 Common Use Cases</h4> <ul style="margin: 0; padding-left: 20px"> <li><strong>Custom Dashboards:</strong> Build executive dashboards with community metrics</li> <li><strong>Content Strategy:</strong> Identify trending topics and high-performing content</li> <li><strong>User Engagement:</strong> Track and reward the most active community members</li> <li><strong>ROI Reporting:</strong> Measure community impact and business value</li> </ul> </div> </div> <div style="background: rgba(240, 255, 240, 1); border-radius: 6px; padding: 12px; margin-top: 15px"><strong>📚 Documentation:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/85193/rest-api-analytics">REST API: Analytics</a>&nbsp;</div> </div> <h2>Getting Help</h2> <p>If you need assistance, <a href="https://my.axerosolutions.com/spaces/77/axero-online-support/cases/add-edit-case/0">open a support case</a> and include the following information:</p> <ul> <li><strong>API Endpoint:</strong> The specific endpoint and HTTP method (e.g., <code>GET /api/users/me</code>)</li> <li><strong>Request Headers:</strong> Include all headers, but redact your actual token</li> <li><strong>Request Body:</strong> Include the request payload if applicable</li> <li><strong>Error Response:</strong> Complete error message with HTTP status code</li> <li><strong>Network Environment:</strong> Any firewalls, proxies, or network restrictions</li> </ul>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108975/using-the-rest-api',
            author: 'Maxwell Drain',
            created: '2025-07-14T02:38:09.017',
            modified: '2025-07-14T14:28:38.437',
          },
        ],
      },
      'examples-tutorials': {
        title: 'Examples & Tutorials',
        icon: 'fas fa-book',
        pages: [
          {
            id: 108852,
            title: 'Create Journey',
            slug: 'create-journey',
            category: 'examples',
            content: '<h1 id="create-journey">Create Journey</h1>\n<p>Creating a journey involves designing a structured sequence of steps that guide users through content, tasks, or milestones. Using the drag-and-drop builder, managers and&nbsp;admins can arrange steps into a vertical flow, reorder them at any time, or group related steps into segments. Journey blocks are grouped into three categories: User Interaction, Communication, and Automation. Each category defines a different type of action users complete at a specific point in the journey.</p> <ol> <li> <p>From the Journey Manager dashboard, click<strong> Create Journey.</strong></p> <p><img alt="Click Create Journey" src="https://my.axerosolutions.com/attachment?file=Qdlv1VNMVnfjh13xTyYw3w%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="566" width="1440"></p> </li> <li> <p><strong>Enter a name</strong> for the journey, and <strong>add a description</strong> to explain its purpose and audience.</p> <p><img alt="Filled-in Name and Description fields" src="https://my.axerosolutions.com/attachment?file=8bEeGi9sK0bSkVr5SV4xRQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="570" width="1440"></p> </li> <li> <p>Design the journey flow by dragging blocks from the left panel into the canvas. Steps are stacked vertically, in the order users will follow them. Blocks are grouped into three categories:&nbsp;</p> <ul> <li><strong>User Interaction:</strong> Requires the user to view content such as articles, visit links, or read custom instructions created within the step.</li> <li><strong>Communication: </strong>Sends an email or in-app notification to the participant at the defined step.</li> <li><strong>Automation: </strong>Controls the journey flow through timed delays, space membership changes, or progress milestones.</li> </ul> </li> </ol> <h3 id="steps"><span style="color: rgba(0, 0, 0, 1)">User Interaction Steps</span></h3> <p>Steps define the individual actions a user completes throughout the journey. These can include viewing content, visiting links, or reading custom instructions. Steps can be grouped into segments to organize them by theme or phase.</p> <h4>Add Step</h4> <p>Adding a step allows you to define what the user should view or complete at a specific stage of the journey. Steps can guide participants through actions such as reading content, accessing external resources, or viewing custom instructions. Each step includes a name, description, and an optional requirement for completion before moving forward.</p> <ol> <li>From the left panel, drag the <strong>Add Step</strong> block into the journey sequence area. Once added, it will appear as a numbered step. <p><img alt="Journey example" src="https://my.axerosolutions.com/attachment?file=23zXH7EVW%2BOgyaNHLAuvcw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="642" width="1440"></p> </li> <li>Enter a step <strong>Name</strong> and <strong>Description</strong>.<br><strong></strong> <br> <p><em>Note: </em>The journey name and description you enter will be shown to participants in the My Journeys panel. Use clear, meaningful titles and descriptions to help users understand the purpose and content of the journey from the start.</p> <p><img alt="Add Step" src="https://my.axerosolutions.com/attachment?file=8iJh5PI776riXGWGuoTk9A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1200" width="1420"></p> </li> <li> <p>Select a Step Type based on what users need to view, access, or complete at this stage of the journey.</p> <ul> <li> <p><strong>View Content</strong><br>By selecting this step type, you can direct users to read existing content published on your platform. Use the filter icon to narrow your search by <strong>space</strong> and <strong>content type</strong> before entering a keyword or title in the search bar. Supported content types include: articles, blogs, files, videos, and wikis.</p> <p><em>Note:</em> If the selected content is in a private space, a prompt will appear asking whether to add a step that grants participants access to the space before they reach the content step.</p> <p><img alt="View Content" src="https://my.axerosolutions.com/attachment?file=jEyrk%2F4K83Q25NA%2BTccTHg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1186" width="1422"></p> </li> <li> <p><strong>Visit Link</strong><br>Enter a URL that directs users to an internal or external page, document, or resource they should visit as part of this step. At the bottom of the panel, check the box if you want to require completion before the user can move forward.</p> <p><img alt="Visit Link" src="https://my.axerosolutions.com/attachment?file=CYCsAhEQLrOeCNqtecATfA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1268" width="1426"></p> </li> <li> <p><strong>Custom Content</strong><br>Use the rich-text editor to create content that displays directly inside the step. This is useful for welcome messages, milestone notes, or one-off instructions. The content is only visible within the journey and is not published elsewhere.</p> <p><em>Note</em><strong>:</strong> Custom content may include external resources that cannot be automatically tracked; users must manually mark this step as complete.</p> <p><img alt="Custom Content" src="https://my.axerosolutions.com/attachment?file=WZ1Rn5zPrIFK25b5bWHzBA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1546" width="1412"></p> </li> </ul> </li> <li> <p>Click&nbsp;<strong>Save</strong> to add the step to your journey sequence.</p> </li> </ol> <h4>Add Segment</h4> <p>Adding a segment lets you organize multiple steps into a single group within the journey. Segments allow you to structure the journey by breaking it down into defined phases, helping users progress through stages such as "Week 1," "Orientation," or "Final Tasks." Each segment includes a name, description, and a setting that determines whether steps must be completed in order or in any sequence.</p> <ol> <li> <p>From the left panel, drag the <strong>Add Segment</strong> block into the sequence area. A new segment panel will appear, ready for configuration.</p> </li> <li> <p>Enter a clear name that describes the purpose of the segment, and provide a description explaining what this group of steps is about.<br><br><img alt="Step Group" src="https://my.axerosolutions.com/attachment?file=w3AuJyjlGA%2FZ53moRwQCeA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="650" width="1428"></p> </li> <li> <p>Select how users should progress through the steps within this segment:</p> <ul> <li><strong>Sequential</strong> - Steps must be completed in the defined order.</li> <li><strong>Non-sequential</strong> - Users can complete steps in any order.</li> </ul> <p><em>Note</em>: If enabled, the setting <strong>Require step completion before progression</strong> on individual steps takes precedence over the segment (step group) setting when determining the order of completion. Even if a segment is set to allow steps to be completed in any order, steps with this setting active must be completed sequentially before progressing.</p> </li> <li> <p>Add at least one step (such as Add Step or Send Email) into the segment panel.</p> <p><img alt="Add steps" src="https://my.axerosolutions.com/attachment?file=D6d25ywt8HVFTz0WK0fL%2Bw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="654" width="1440"></p> </li> <li> <p>Once steps are added and details are configured, click&nbsp;<strong>Save Journey</strong>.</p> </li> </ol> <h3 id="communication-steps">Communication Steps</h3> <p>Communication steps help you keep participants or their managers informed, engaged, or encouraged as they move through the journey. These can be used to send instructional emails, motivational reminders, or in-app alerts, helping users stay on track and informed throughout each phase.</p> <h4>Send Email</h4> <p>Configure an email message to be sent to the participant or their manager at a specific point in the journey. The message can include instructions, reminders, or motivational content aligned with the journey’s purpose.</p> <p><img alt="" src="https://my.axerosolutions.com/attachment?file=1WeUkKNppUfrNd%2BnCzrywA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1482" width="1438"></p> <ol> <li> <p>From the left panel, drag <strong>Send Email</strong> into the journey flow and place it where the email should be triggered.</p> </li> <li> <p>Give the step a clear name that reflects its purpose. <em>Example</em>: Reminder Email After Week One.</p> </li> <li> <p>Choose the recipient from the dropdown menu. You can send the email to individual participants, their manager, or a department.</p> </li> <li> <p>Compose the Email:</p> <ul> <li><strong>Subject:</strong> Enter the email subject line that will appear to recipients. Use tokens from the dropdown to personalize the message. Example: <code>Welcome, [First Name]!</code></li> <li><strong>CC / BCC (Optional):</strong> Add any additional email addresses for visibility or tracking.</li> <li><strong>Email Wrapper:</strong> Select a wrapper layout to apply a standard header/footer. This helps match your community or brand style. For more information, see how wrappers are managed in the <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/102873/email-template-manager#email-wrapper">Email Template Manager</a>.</li> <li><strong>Email Body:</strong> Use the rich-text editor to compose your message. You can format text, insert links or images, and include profile-based tokens from the "Recipient" dropdown (example: <code>City</code>, <code>Department</code>, or custom fields).</li> </ul> </li> <li> <p>Click <strong>Save Changes</strong> to add the email to your journey.</p> </li> </ol> <h4>Send Notification</h4> <p>Send a brief message to the participant through an in-app notification, email, or both. The notification is triggered when the participant reaches this point in the journey. You can use it to send reminders, brief updates, or time-sensitive alerts to help guide participants through the process.</p> <p><img alt="" src="https://my.axerosolutions.com/attachment?file=4HwtxMS4%2FGTf4hXfiZHpqA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1500" width="1424"></p> <ol> <li> <p>Drag <strong>Send Notification</strong> into the journey flow.</p> </li> <li> <p><strong>Enter a step name</strong> that clearly reflects the purpose. (<em>Example</em>: "Don’t forget to complete your next task!").</p> </li> <li> <p>Enter the notification <strong>title</strong> and <strong>message</strong>. Select a recipient token from the dropdown to personalize the message.</p> <ol></ol> </li> <li><strong>Preview</strong> how the title and message will appear to users.</li> <li> <p>Select how the notification should be delivered: in-app, by email, or both.</p> </li> <li> <p>Choose whether to send the notification after the step is reached or after a delay.</p> </li> <li> <p>Save the Step.</p> </li> </ol> <h3 id="automation-steps">Automation Steps</h3> <p>Automation steps help control the flow of the journey by managing timing, space access, and milestone recognition. They pace the experience, grant or remove access to resources, and celebrate participant progress without requiring manual action from administrators.</p> <h4>Add Delay</h4> <p>Add a delay to pause the journey for a specific amount of time before participants continue to the next step. This provides users with time to engage with content or complete previous tasks.</p> <p><em>Note</em>: Delay steps pause progress for a specified duration and are intended for use within segments (step groups) that require steps to be completed sequentially, following the defined order. When a delay step is used within a segment that allows steps to be completed in <strong>any order</strong>, the delay step temporarily locks all subsequent steps.</p> <p><img alt="Add Delay" src="https://my.axerosolutions.com/attachment?file=OOxldDHbYQNzIu1CniMhfA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="544" width="1440"></p> <ol> <li> <p>Drag <strong>Add Delay</strong> into the journey sequence, where you want the pause to occur.&nbsp;</p> </li> <li> <p>Enter the delay duration using minutes, hours, or days. The next step appears once this time passes.</p> </li> <li> <p><strong>Save the Step.</strong></p> </li> </ol> <h4 id="add-space">Add Space Access</h4> <p>Manage a participant\'s access to a specific space based on their progress in the journey. Use this step to grant or revoke access automatically. For example, grant temporary access to a space that contains training materials or&nbsp;onboarding tasks, then revoke the participant\'s access after the relevant segment is complete.</p> <p><img alt="Add Space Access" src="https://my.axerosolutions.com/attachment?file=9FqQFoifl8rpAH1oF6KDuQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="760" width="1430"></p> <ol> <li> <p>From the left panel, drag <strong>Add to Space</strong> into the journey sequence at the point where the change should occur.</p> </li> <li>Enter a name for the step.</li> <li><strong>Select</strong> the space from the dropdown menu.</li> <li> <p>Choose the action:</p> <ul> <li><strong>Add to Space:</strong> The user will automatically be added to the space at this step.</li> <li><strong>Remove from Space:</strong> The user will automatically be removed from the space when they reach this point in the journey.</li> </ul> </li> <li> <p>Click <strong>Save Changes</strong> to apply the change and continue building your journey.</p> </li> </ol> <h4>Celebrate Achievement</h4> <p>Add recognition to the journey to celebrate progress or mark key moments with a personalized message or badge. This step supports participant motivation and highlights their achievements in a meaningful way.</p> <ol> <li>From the left panel, drag the <strong>Celebrate Achievement</strong> block into the journey sequence where you want to recognize progress or completion.</li> <li>Enter a clear step name for internal tracking (example: <em>Completed Orientation</em>).</li> <li>Select a celebration type by choosing one of the following options: <ul> <li><strong>a. Notification</strong><br>If you selected <em>Notification</em>, add a short, uplifting title (example: <em>Congratulations!</em>) and write a personalized message that will appear to the user when the step is completed.<br> <p><img alt="Notification" src="https://my.axerosolutions.com/attachment?file=vYVq98lM6g1Fw2ImL2lm1A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1204" width="1428"></p> </li> <li><strong>b. Badge</strong><br>If you selected <em><a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/5952/badges">Badge</a> </em>as the celebration type, you’ll be able to award users with a digital badge when they reach this step. Choose a <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/38420/recognition-programs">Recognition Program</a> from the&nbsp;dropdown, select the badge you\'d like to assign, and enter the number of points to award along with it.<br> <p><img alt="" src="https://my.axerosolutions.com/attachment?file=irsXxETaQc%2BP%2F5VigW63Eg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="892" width="1428"></p> </li> </ul> </li> <li>Click <strong>Save Changes</strong> to confirm the setup and add the step to the journey.</li> </ol>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108852/create-journey',
            author: 'Vladana Garic',
            created: '2025-06-30T11:53:28.617',
            modified: '2025-07-04T07:57:51.24',
          },
          {
            id: 107911,
            title: 'Create Wiki Page',
            slug: 'create-wiki-page',
            category: 'examples',
            content: '<h1 id="create-wiki-page">Create Wiki Page</h1>\n<p><img alt="Click create in the header" src="https://my.axerosolutions.com/attachment?file=Lh0XYPy5sq7ZhOAJTkMEzQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="822" width="1440"></p> <p><img alt="Click Wiki page" src="https://my.axerosolutions.com/attachment?file=wgxwDIiJCFAW6Cj2WB1V9A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="874" width="1440"></p> <p>In the modal that opens, select the space you want to post to.</p> <p><img alt="Select a space" src="https://my.axerosolutions.com/attachment?file=qQOjFbzk7IqampuMSzms6A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="692" width="1228"></p> <p><img alt="Fill in required details" src="https://my.axerosolutions.com/attachment?file=HuFfsk71M5evR%2FgMltfmvg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1324" width="1440"></p> <p>Fill in required sections:</p> <ul> <li><strong>Page name</strong>: The&nbsp;title of the wiki.</li> <li><strong>Summary</strong>: A summary that will appear beside the wiki in the&nbsp;{{mention:21539:9}}&nbsp;.</li> <li><strong>Page content</strong>: Wiki&nbsp;main body.</li> <li><strong>Parent topic</strong>: The page your page will be sorted under. As you type in this section, suggestions of existing pages will appear in a dropdown menu.</li> </ul> <p>Fill in optional sections:</p> <ul> <li><strong>Upload a featured image</strong>:&nbsp;An image that will appear beside the wiki in the Activity Stream. You can upload an image from your computer, your albums, your clipboard, or your webcam.&nbsp;Suggested aspect ratio: 4:3</li> <li><strong>Add tags</strong>: Keywords that can help others find your wiki. Use spaces to separate tags. Use an underscore or dash to combine separate words into one tag (e.g., Vacation-Policy). Click <em class="icon-plus-sign-alt"></em> to see Popular Tags&nbsp;and Tag Groups.</li> <li><strong>Meta information&nbsp;- Meta title, Meta description</strong>:&nbsp;Information to help users find the wiki.</li> <li><strong>Author</strong>:&nbsp;Set an author to&nbsp;{{mention:34150:9}}.</li> <li><strong>Attach files</strong>:&nbsp;Add files relevant to the wiki. Drag and drop files into the area, or click Select Files.</li> <li><strong>Publication Date:&nbsp;</strong>Set the wiki&nbsp;to publish at a certain time and date.</li> <li><strong>Expiration date:</strong> Set the wiki to expire at a certain time and date.</li> </ul> <p>Options:</p> <ul> <li><strong>Featured</strong>:&nbsp;Make the wiki&nbsp;a featured post in the space.</li> <li><strong>Required reading</strong>: Require users to mark the wiki as read.</li> <li><strong>Allow comments</strong>: Allow users to comment on the wiki.</li> <li><strong>Allow non-members comments</strong>: Allow users who are not logged in to comment.</li> <li><strong>Allow likes</strong>: Allow users to like the wiki and comments on the wiki.</li> <li><strong>Allow ratings</strong>: Allow users to rate the wiki and comments on the wiki.</li> </ul> <p>To create collapsible&nbsp;content in your wiki, open Tools &gt; Source Code in the editor and insert the HTML code below.&nbsp;<span class="ax-role-site-administrator">(For security reasons, we filter out some HTML elements by default. You will need to add the attributes <code>data-toggle</code> and <code>data-target</code> to&nbsp;HTMLSanitizeAllowedAttributes&nbsp;in&nbsp;{{mention:334:9}}&nbsp;&gt;&nbsp;{{mention:335:9}}&nbsp;&gt;&nbsp;{{mention:22317:9}}&nbsp;.)</span></p> <pre><code class="html">&lt;a data-toggle="collapse" data-target="#demo"&gt;Click this to open/close&lt;/a&gt; &lt;div id="demo" class="collapse in"&gt; Your collapsed content should go in here. &lt;/div&gt;</code></pre> <p><img alt="Save and Publish" src="https://my.axerosolutions.com/attachment?file=S2SSOQqmIbcB215pV1VFCw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="923" width="1440"></p> <p>Once you\'ve finished creating your wiki page, select one of the following options:</p> <ul> <li>Save and Publish</li> <li>Cancel</li> <li>{{mention:23816:9}}: Save&nbsp;the wiki page for later. You can find your drafts in&nbsp;{{mention:21494:9}}&nbsp;&gt;&nbsp;{{mention:21528:9}}&nbsp;</li> </ul> <h2><img alt="Activity Stream &gt; My Content" src="https://my.axerosolutions.com/attachment?file=yt67%2BYvbtzjGPsEYcJiQ%2Bw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1148" width="1440"></h2> <h2><img alt="My Content &gt; Wikis" src="https://my.axerosolutions.com/attachment?file=63r2Dk7UydK9drAK2cj4Xg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="837" width="1440"></h2> <h2>Alternative Way</h2> <p><img alt="Click wiki in the space menu" src="https://my.axerosolutions.com/attachment?file=kvgMhQxkJdVydvbdaMfYHQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="970" width="1440"></p> <p><img alt="Click Add Wiki page" src="https://my.axerosolutions.com/attachment?file=1B3NjawM3vN8jcYMhs5Qpg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="744" width="1440"></p> <h2>Related</h2> <p>{{mention:38473:9}}&nbsp;</p> <p>You\'ve been given a task to share information with the entire organization, and you want to quickly and easily share the new exciting information with everyone by posting it on your homepage. In this guide, you\'ll learn how to make important information visible on your homepage.</p> <p>{{mention:26880:9}}&nbsp;</p> <p>Generate a table of contents based on the headings in your content. The table of contents will display the main headings in your content. People can quickly jump right to specific sections in the content using the table of contents.</p> <p>{{mention:26842:9}}&nbsp;</p> <p>You can create document templates to be used in the&nbsp;rich text editor. This allows your site administrator or space administrators to create HTML&nbsp;templates that&nbsp;users can select from a dropdown and insert into the editor&nbsp;textarea. If you create a lot of documents using a uniform layout, this saves you time and effort by creating a standard HTML layout for your template.</p>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107911/create-wiki-page',
            author: 'Maxwell Drain',
            created: '2025-04-07T12:02:11.71',
            modified: '2025-04-10T11:07:12.32',
          },
          {
            id: 108973,
            title: 'Creating Bearer Tokens',
            slug: 'creating-bearer-tokens',
            category: 'examples',
            content: '<h1 id="creating-bearer-tokens">Creating Bearer Tokens</h1>\n<p>JWT Bearer Tokens provide enhanced security and management capabilities for REST API authentication in Axero. Available starting with <strong>Axero version 9.45</strong>, these cryptographically signed tokens replace traditional API keys and follow industry-standard protocols (RFC 7519) for secure API access.</p> <blockquote>📋 <strong>Administrator Prerequisites:</strong> JWT Bearer Token functionality must be enabled by an administrator through <strong>Control Panel &gt; System &gt; System Properties</strong> with the settings <code>EnableLegacyAPIKey = false</code> and <code>EmulateLegacyAPIKey = true</code>. See <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108972/enabling-api-access">Enabling REST API Access</a> for complete setup instructions.</blockquote> <blockquote>🔑 <strong>User Permission Requirements:</strong> Your user role must have REST API access enabled by an administrator. If you cannot access the Authorizations section, contact your system administrator to enable API permissions for your role.</blockquote> <p>Once prerequisites are met, users can create and manage JWT Bearer Tokens through the <strong>Authorizations</strong> section in their account settings. This self-service interface provides secure token lifecycle management for API integrations with external applications and tools.</p> <hr> <h3>Creating Your First JWT Bearer Token</h3> <p>Follow these step-by-step instructions to create a new JWT Bearer Token for your API integrations:</p> <ol> <li><strong>Access Your Profile Menu:</strong> Click on your profile avatar in the top right corner of the screen to open the user menu.<br><br><img alt="Access your profile menu" src="https://my.axerosolutions.com/attachment?file=4LVxpscW%2Bat9nsfdW8anHQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="732" width="1440"></li> <li><strong>Navigate to My Account:</strong> From the dropdown menu, select <strong>Activity Stream</strong> to access your account settings.<br><br><img alt="Go to activity stream" src="https://my.axerosolutions.com/attachment?file=TZ2a97LxTfO5E%2FWgcnnOqQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="742" width="1440"></li> <li><strong>Open Integrations Section:</strong> In the left sidebar navigation, locate and click <strong>Integrations</strong>.<br><br><img alt="Go to Integrations" src="https://my.axerosolutions.com/attachment?file=MYZGiqj7EKadjoUi2RYFKA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="780" width="1440"></li> <li><strong>Access Authorizations:</strong> Within the Integrations section, navigate to the <strong>Authorizations</strong> tab. This is your JWT Bearer Token management center where you can create, view, and manage all your API tokens.</li> <li><strong>Initiate Token Creation:</strong> Click the <strong>Add Authorization</strong> button. A new token configuration form will appear at the top of your token list.<br><br><img alt="Click the Add autorization button" src="https://my.axerosolutions.com/attachment?file=b%2FCpgjstG7jhr73SEPKkoA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="659" width="1440"></li> <li><strong>Configure Token Settings:</strong> Complete the token configuration form with the following information: <h4>Token Name (Required)</h4> <p>Enter a descriptive, meaningful name that clearly identifies the token\'s purpose and intended use. Good naming practices help with organization and security auditing.</p> <div style="background: rgba(248, 249, 250, 1); border-left: 4px solid rgba(40, 167, 69, 1); padding: 15px; margin: 15px 0"> <p><strong>✅ Good Token Name Examples:</strong></p> <ul> <li><strong>"Analytics Dashboard Integration"</strong> - Specific purpose and system</li> <li><strong>"Third-party CRM Sync - Production"</strong> - Purpose and environment</li> <li><strong>"Automated Reporting System - Q4 2025"</strong> - Purpose and timeframe</li> <li><strong>"Mobile App Backend - iOS"</strong> - Application and platform</li> </ul> </div> <h4>Creation Date</h4> <p>Automatically populated with the current date and time when the token is generated. This helps track token age and lifecycle.</p> <h4>Expiration Period</h4> <p>Select an appropriate expiration timeframe based on your integration needs. Available options depend on your administrator\'s security settings:</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h5>🕐 Short-term (Minutes to Hours)</h5> <ul> <li><strong>Best for:</strong> Testing, development, temporary access</li> <li><strong>Security:</strong> Highest - minimal exposure window</li> <li><strong>Use cases:</strong> API testing, proof of concepts</li> </ul> </div> <div> <h5>📅 Medium-term (Days to Months)</h5> <ul> <li><strong>Best for:</strong> Project-based integrations, seasonal applications</li> <li><strong>Security:</strong> Balanced - defined lifecycle</li> <li><strong>Use cases:</strong> Campaign tools, temporary dashboards</li> </ul> </div> </div> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h5>📆 Long-term (Years)</h5> <ul> <li><strong>Best for:</strong> Stable, long-running integrations</li> <li><strong>Security:</strong> Moderate - requires monitoring</li> <li><strong>Use cases:</strong> Production systems, core integrations</li> </ul> </div> <div> <h5>♾️ Unlimited (No Expiration)</h5> <ul> <li><strong>Best for:</strong> Critical systems requiring continuous access</li> <li><strong>Security:</strong> Requires careful management</li> <li><strong>Availability:</strong> Only if enabled by administrator</li> </ul> </div> </div> </li> <li><strong>Generate the Token:</strong> After configuring all settings, click the <em class="fas fa-check-circle"></em> checkmark icon to save your configuration and generate the JWT Bearer Token.<br><br><img alt="Add Authorization" src="https://my.axerosolutions.com/attachment?file=qzD0oRWO8pF5aMzp2EAruw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="688" width="1440"></li> <li><strong>Secure Token Storage:</strong> The JWT Bearer Token is displayed immediately after generation. <strong>This is your only opportunity to view the complete token value.</strong> <div style="background: rgba(255, 243, 205, 1); border: 1px solid rgba(255, 234, 167, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🔐 Critical Security Step:</strong></p> <ul> <li>Use the clipboard icon to copy the complete token value</li> <li>Store it immediately in a secure location (password manager, encrypted vault, secure configuration)</li> <li>Verify the token is saved before closing this window</li> <li>The token cannot be retrieved again after this step</li> </ul> </div> <img alt="Token" src="https://my.axerosolutions.com/attachment?file=XTFZzNCvCeMq%2BWG99kewqQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="581" width="1440"></li> </ol> <blockquote>⚠️ <strong>Critical Security Reminder:</strong> JWT Bearer Tokens cannot be retrieved after the initial display. The token value is not stored by Axero for security reasons. If you lose a token, you must create a new one and update all applications that use it. Plan accordingly and store tokens in a secure, accessible location.</blockquote> <hr> <h3>Using Your JWT Bearer Token</h3> <p>Once you\'ve created and securely stored your JWT Bearer Token, you can use it to authenticate API requests. The token must be included in the <code>Authorization</code> header of every API call.</p> <h4>Authentication Header Format</h4> <pre><code>Authorization: Bearer YOUR-JWT-TOKEN-HERE</code></pre> <p>Replace <code>YOUR-JWT-TOKEN-HERE</code> with the actual token value you copied during creation.</p> <h4>Example API Requests</h4> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>📝 cURL Example:</strong></p> <pre><code>curl -X GET "https://yoursite.axero.com/api/users/me" \\ -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \\ -H "Content-Type: application/json"</code></pre> </div> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🌐 JavaScript (fetch) Example:</strong></p> <pre><code>fetch(\'https://yoursite.axero.com/api/users/me\', { method: \'GET\', headers: { \'Authorization\': \'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\', \'Content-Type\': \'application/json\' } }) .then(response =&gt; response.json()) .then(data =&gt; console.log(data));</code></pre> </div> <div style="background: rgba(248, 249, 250, 1); border: 1px solid rgba(222, 226, 230, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🐍 Python (requests) Example:</strong></p> <pre><code>import requests headers = { \'Authorization\': \'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\', \'Content-Type\': \'application/json\' } response = requests.get(\'https://yoursite.axero.com/api/users/me\', headers=headers) data = response.json()</code></pre> </div> <hr> <h3>Managing Existing Tokens</h3> <p>The Authorizations section provides comprehensive lifecycle management for all your JWT Bearer Tokens. You can monitor token status, update organization, and maintain security through proper token hygiene.</p> <h4>Token Information Dashboard</h4> <p>For each token in your Authorizations list, you can view important metadata to help manage your API integrations:</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0"> <div> <h5>📋 Available Information</h5> <ul> <li><strong>Token Name:</strong> The descriptive name you assigned during creation</li> <li><strong>Creation Date:</strong> When the token was originally generated</li> <li><strong>Expiration Date:</strong> When the token will automatically expire (if applicable)</li> <li><strong>Status:</strong> Whether the token is active, expired, or revoked</li> </ul> </div> <div> <h5>🔒 Security Features</h5> <ul> <li><strong>Token Value:</strong> Never displayed after creation</li> <li><strong>Usage Tracking:</strong> Monitor when tokens are accessed</li> <li><strong>Instant Revocation:</strong> Immediately disable compromised tokens</li> <li><strong>Expiration Monitoring:</strong> Track approaching expiration dates</li> </ul> </div> </div> <blockquote>🔍 <strong>Security Note:</strong> The actual token value is never displayed after initial creation for enhanced security. Only metadata about the token is shown in the management interface. This prevents accidental exposure and ensures tokens remain secure.</blockquote> <h4>Renaming a Token</h4> <p>Keep your token organization current by updating names to reflect changes in purpose, environment, or usage:</p> <div style="background: rgba(240, 248, 255, 1); border-left: 4px solid rgba(0, 102, 204, 1); padding: 15px; margin: 15px 0"> <p><strong>💡 When to Rename Tokens:</strong></p> <ul> <li>Application or integration purpose changes</li> <li>Moving from development to production environment</li> <li>Transferring token ownership between teams</li> <li>Improving naming consistency across your organization</li> </ul> </div> <ol> <li><strong>Locate the Token:</strong> Find the token you want to rename in your Authorizations list</li> <li><strong>Enter Edit Mode:</strong> Click the <em class="fal fa-pencil"></em> pencil icon next to the token name</li> <li><strong>Update the Name:</strong> Enter a new descriptive name that clearly identifies the token\'s current purpose</li> <li><strong>Save Changes:</strong> Click the <em class="fas fa-check-circle"></em> checkmark icon to save your changes</li> </ol> <p><img alt="Rename token" src="https://my.axerosolutions.com/attachment?file=qoCgck6mixQEi2cnB%2BxbXg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="803" style="margin: 0" width="1435" data-action="zoom"></p> <h4>Revoking a Token</h4> <p>Immediately and permanently disable a token when it\'s no longer needed or if security has been compromised. This is a critical security operation that should be performed carefully.</p> <div style="background: rgba(248, 215, 218, 1); border: 1px solid rgba(245, 198, 203, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🚨 When to Revoke Tokens:</strong></p> <ul> <li><strong>Security Compromise:</strong> Token may have been exposed or stolen</li> <li><strong>Integration Decommissioning:</strong> Application or service is being retired</li> <li><strong>Team Member Departure:</strong> Employee with token access leaves organization</li> <li><strong>Token Rotation:</strong> Replacing with new token as part of security policy</li> <li><strong>Suspicious Activity:</strong> Unusual API usage patterns detected</li> </ul> </div> <ol> <li><strong>Identify the Token:</strong> Find the token you want to disable in your Authorizations list</li> <li><strong>Initiate Revocation:</strong> Click the <strong>trash icon</strong> next to the token entry to revoke it</li> <li><strong>Confirm Action:</strong> Review the confirmation dialog and confirm the revocation</li> <li><strong>Immediate Effect:</strong> The token is instantly invalidated - all API calls using it will fail with authentication errors</li> </ol> <p><img alt="Revoke" src="https://my.axerosolutions.com/attachment?file=oPoAUa0I%2BEIrdUoc2w%2FTog%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="580" width="1440"></p> <blockquote>⚠️ <strong>Critical Warning:</strong> Token revocation is immediate and irreversible. Before revoking a token, ensure all applications using it are updated with a replacement token to prevent service disruptions. Consider creating and testing the new token before revoking the old one.</blockquote> <hr> <h3>Token Security Best Practices</h3> <p>Following these security guidelines ensures your JWT Bearer Tokens remain secure and your API integrations are protected:</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0"> <div> <ul style="list-style: none; margin-left: 5px"> <li>✅ Store tokens securely (environment variables, secure vaults)</li> <li>✅ Use HTTPS for all API requests</li> <li>✅ Implement token rotation and refresh mechanisms</li> <li>✅ Monitor token usage and revoke compromised tokens</li> <li>✅ Regularly review and revoke unused tokens</li> <li>✅ Set shorter expiration periods for development/testing</li> <li>✅ Use descriptive names that include the application or service</li> <li>✅ Keep an inventory of active tokens for security auditing</li> </ul> </div> <div> <ul style="list-style: none; margin-left: 5px"> <li>❌ Never commit tokens to version control</li> <li>❌ Don\'t expose tokens in client-side code</li> <li>❌ Don\'t share tokens between different applications</li> <li>❌ Don\'t ignore token expiration warnings</li> <li>❌ Don\'t use tokens with overly broad permissions</li> <li>❌ Don\'t store tokens in plain text files</li> <li>❌ Don\'t forget to revoke tokens when no longer needed</li> </ul> </div> </div> <div style="background: rgba(255, 243, 205, 1); border: 1px solid rgba(255, 234, 167, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🔒 Additional Security Recommendations:</strong></p> <ul> <li><strong>Token Rotation:</strong> Implement regular token rotation schedules for long-running integrations</li> <li><strong>Access Monitoring:</strong> Log and monitor API access patterns to detect unusual activity</li> <li><strong>Principle of Least Privilege:</strong> Only grant the minimum permissions necessary for each integration</li> <li><strong>Incident Response:</strong> Have a plan for quickly revoking and replacing tokens if security is compromised</li> </ul> </div> <h3>Next Steps</h3> <p>After creating your JWT Bearer Token, follow these steps to ensure successful implementation:</p> <ol> <li><strong>Test the Token:</strong> Verify it works with a simple API call using tools like Postman, curl, or your development environment</li> <li><strong>Update Applications:</strong> Replace any legacy API keys with the new JWT token in your application configurations</li> <li><strong>Document Token Usage:</strong> Record which applications and services are using each token for future reference</li> <li><strong>Monitor Usage:</strong> Regularly review token activity and expiration dates in the Authorizations section</li> <li><strong>Plan Renewals:</strong> Set calendar reminders before tokens expire to ensure uninterrupted service</li> </ol>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108973/creating-bearer-tokens',
            author: 'Maxwell Drain',
            created: '2025-07-14T01:30:43.58',
            modified: '2025-07-16T09:33:25.763',
          },
          {
            id: 108972,
            title: 'Enabling API Access',
            slug: 'enabling-api-access',
            category: 'examples',
            content: '<h1 id="enabling-api-access">Enabling API Access</h1>\n<p>Before users can create JWT Bearer Tokens and access the Axero REST API, an administrator must enable API access and configure appropriate security settings. This guide walks through the complete setup process, from enabling JWT support to configuring security levels that match your organization\'s requirements.</p> <blockquote>📋 <strong>Prerequisites:</strong> You must have administrator privileges to configure REST API settings. These changes affect all users in your organization and should be planned accordingly.</blockquote> <hr> <h3>Step 1: Enable JWT Bearer Token Support</h3> <p>To enable JWT authentication and unlock advanced security options, you need to configure specific system properties:</p> <ol> <li><strong>Navigate to System Properties:</strong> Go to <strong>Control Panel &gt; System &gt; System Properties</strong></li> <li><strong>Configure JWT Settings:</strong> Set the following properties to enable JWT Bearer Token functionality: <ul> <li><code>EnableLegacyAPIKey = false</code> - Disables legacy API key creation in user preferences</li> <li><code>EmulateLegacyAPIKey = true</code> - Enables JWT token management in the Integrations section</li> </ul> </li> <li><strong>Save Changes:</strong> Apply the configuration to activate JWT support</li> </ol> <p><img alt="Enable JWT Settings" src="https://my.axerosolutions.com/attachment?file=Ddt%2BQVmIexTCSmHH5gnw7g%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="370" style="margin: 0" width="1435" data-action="zoom"></p> <hr> <h3>Step 2: Configure Security Level</h3> <p>After enabling JWT support, configure the appropriate security level for your organization. Navigate to <strong>REST API Settings</strong> and choose from three security options, each offering different levels of protection and compatibility:</p> <h3>🔁 Legacy Security (Transitional)</h3> <p>Maintains backward compatibility by supporting both legacy API keys and JWT tokens during the migration period.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 0"> <div> <h4>✅ Supported Features</h4> <ul> <li>Legacy API keys (read-only)</li> <li>JWT Bearer Tokens</li> <li>Username/password authentication</li> <li>All existing integrations continue working</li> </ul> </div> <div> <h4>⚠️ Important Considerations</h4> <ul> <li>Recommended for transition period only</li> <li>Legacy support ends <strong>November 2025</strong></li> <li>Automatic upgrade to High Security after deprecation</li> <li>Plan migration to JWT tokens during this phase</li> </ul> </div> </div> <p><img alt="Legacy Security Screenshot" src="https://my.axerosolutions.com/attachment?file=t5yDLLNphlwXMdXnB7kX9Q%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="899" style="margin: 0" width="1435" data-action="zoom"></p> <h3>🔐 High Security (Recommended)</h3> <p>The recommended security level that eliminates legacy API keys while maintaining compatibility for essential third-party integrations. This mode provides enhanced JWT token management with configurable expiration controls.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 0"> <div> <h4>✅ Supported Features</h4> <ul> <li>JWT Bearer Tokens with full lifecycle management</li> <li>Username/password authentication for third-party tools</li> <li>Configurable token expiration settings</li> <li>Enhanced security monitoring and controls</li> </ul> </div> <div> <h4>❌ Disabled Features</h4> <ul> <li>Legacy API key creation and editing</li> <li>API key visibility in user preferences</li> <li>Existing API keys stop functioning</li> </ul> </div> </div> <p><img alt="High Security Screenshot" src="https://my.axerosolutions.com/attachment?file=45U90UIF2vsMSuMKBEPGuA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="861" style="margin: 0" width="1435" data-action="zoom"></p> <blockquote>⚠️ <strong>Migration Required:</strong> Before enabling High Security, ensure all applications using legacy API keys are updated to use JWT Bearer Tokens. Legacy API keys will immediately stop functioning.</blockquote> <h4>JWT Token Expiration Configuration</h4> <p>High Security mode provides granular control over token expiration policies:</p> <ul> <li><strong>Default Expiration:</strong> Set a default expiration period that pre-fills when users create new tokens (e.g., 1 year, 6 months)</li> <li><strong>Maximum Expiration:</strong> Enforce an organizational maximum for token duration to maintain security compliance</li> <li><strong>Allow Unexpiring Tokens:</strong> Optionally permit tokens without expiration dates for specific use cases (use with caution)</li> </ul> <div style="background: rgba(255, 243, 205, 1); border: 1px solid rgba(255, 234, 167, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🔧 Configuration Best Practices:</strong></p> <ul> <li><strong>Default Expiration:</strong> Set to 1 year for most organizations, shorter for high-security environments</li> <li><strong>Maximum Expiration:</strong> Consider 2-3 years maximum to balance security and operational needs</li> <li><strong>Unexpiring Tokens:</strong> Only enable if absolutely necessary for critical system integrations</li> </ul> </div> <h3>🔒 Extra High Security (Maximum Protection)</h3> <p>The highest security level that provides maximum protection by requiring JWT Bearer Tokens for all API access. This mode completely disables username/password authentication, ensuring all API requests use cryptographically signed tokens.</p> <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 0"> <div> <h4>✅ Enhanced Security Features</h4> <ul> <li>JWT Bearer Tokens only — no password authentication</li> <li>Maximum cryptographic security</li> <li>Complete audit trail for all API access</li> <li>Eliminates password-based attack vectors</li> <li>Ideal for high-security environments</li> </ul> </div> <div> <h4>⚠️ Compatibility Limitations</h4> <ul> <li>Third-party tools requiring username/password will not work</li> <li>Official mobile app becomes non-functional</li> <li>All integrations must support JWT tokens</li> <li>Requires comprehensive testing before implementation</li> </ul> </div> </div> <p><img alt="Extra High Security Screenshot" src="https://my.axerosolutions.com/attachment?file=k5i%2BJ6R4yUK90Kb%2FQIOlUA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="857" style="margin: 0" width="1435" data-action="zoom"></p> <div style="background: rgba(248, 215, 218, 1); border: 1px solid rgba(245, 198, 203, 1); border-radius: 8px; padding: 15px; margin: 15px 0"> <p><strong>🚨 Critical Considerations Before Enabling:</strong></p> <ul> <li><strong>Mobile App Impact:</strong> The official Axero mobile app will stop working until it\'s updated to support JWT authentication</li> <li><strong>Third-Party Integration Testing:</strong> Verify all external tools and integrations support JWT Bearer Tokens</li> <li><strong>User Training:</strong> Ensure users understand the new authentication requirements</li> <li><strong>Rollback Plan:</strong> Have a plan to revert to High Security if issues arise</li> </ul> </div> <hr> <h3>Step 3: Enable User Role Permissions</h3> <p>After configuring security levels, ensure that user roles have the appropriate REST API permissions:</p> <ol> <li><strong>Navigate to Role Management:</strong> Go to <strong>Control Panel &gt; Users &gt; Roles</strong></li> <li><strong>Select User Roles:</strong> Choose the roles that should have REST API access</li> <li><strong>Enable API Permissions:</strong> Grant "REST API Access" permission to selected roles</li> <li><strong>Save Changes:</strong> Apply the permission changes</li> </ol> <blockquote>🔑 <strong>Permission Note:</strong> Users without REST API permissions will not see the Authorizations section in their account settings and cannot create JWT tokens.</blockquote> <p>Once JWT support is enabled and user permissions are configured, users can create their own JWT Bearer Tokens: <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108973/creating-bearer-tokens">Generating JWT Bearer Tokens</a></p> <hr> <h3>Next Steps</h3> <p>After completing the REST API setup:</p> <ol> <li><strong>Test Configuration:</strong> Verify that authorized users can access the Authorizations section</li> <li><strong>Create Test Tokens:</strong> Have users create test JWT tokens to validate functionality</li> <li><strong>Update Documentation:</strong> Inform your team about the new token creation process</li> <li><strong>Monitor Usage:</strong> Track token creation and usage patterns for security compliance</li> </ol>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108972/enabling-api-access',
            author: 'Maxwell Drain',
            created: '2025-07-14T00:51:33.777',
            modified: '2025-07-14T14:26:34.703',
          },
          {
            id: 106976,
            title: 'File Field',
            slug: 'file-field',
            category: 'examples',
            content: '<h1 id="file-field">File Field</h1>\n<p>Use a file field to allow users to upload and manage files in user profiles. You can specify the types of files allowed, the maximum file upload size, and the number of files a user can upload.</p> <h2><img alt="File Field" src="https://my.axerosolutions.com/attachment?file=v9I4J7trhuCwu31lzh1gFA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="662" width="1094" data-action="zoom"></h2> <h2>Properties</h2> <table> <tbody> <tr> <td style="width: 50%"><strong>Property</strong></td> <td style="width: 50%"><strong>Description</strong></td> </tr> <tr> <td style="width: 50%">Field label</td> <td style="width: 50%">The field label appears at the top of the field. Start typing, and a list of matching text resources will appear. Select one to use or finish typing to create a new resource when the page is saved.</td> </tr> <tr> <td style="width: 50%">Number required</td> <td style="width: 50%">The minimum number of files a user is required to provide.</td> </tr> <tr> <td style="width: 50%">Number permitted</td> <td style="width: 50%">The maximum number of files a user can upload (typically 1). Enter <code>0</code> for unlimited uploads.</td> </tr> <tr> <td style="width: 50%">Permitted file types</td> <td style="width: 50%">A list of file types allowed for upload. Provide a comma-separated list of acceptable file extensions (without the dot, e.g., "pdf, docx, png") to specify the file types allowed for upload. Leave blank to use the site-wide permitted file types or to allow all file types.</td> </tr> <tr> <td style="width: 50%">Maximum file upload size (MB)</td> <td style="width: 50%">The maximum size of a single file allowed for upload, in megabytes.</td> </tr> </tbody> </table> <h2>Field Help</h2> <p>Text to help users understand what is expected when uploading a file. For example: "Please upload a document no larger than 10MB. Allowed formats: PDF, DOCX."</p> <h2>Advanced</h2> <table style="width: 100%; height: 150.844px"> <tbody> <tr style="height: 31.9688px"> <td style="width: 50%"><strong>Field</strong></td> <td style="width: 50%"><strong>Description</strong></td> </tr> <tr style="height: 54.9375px"> <td style="width: 50%">Custom CSS class</td> <td style="width: 50%">A CSS class to append to the field to apply custom styling.</td> </tr> <tr style="height: 31.9688px"> <td style="width: 50%">Show Label</td> <td style="width: 50%">Check to show the field\'s label.</td> </tr> <tr style="height: 31.9688px"> <td style="width: 50%">Create/Update</td> <td style="width: 50%">Select whether the field should appear only when creating new content or only when updating existing content.</td> </tr> </tbody> </table> <h2>Roles</h2> <p>Choose the roles that can view this field.<strong></strong></p>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106976/file-field',
            author: 'Vladana Garic',
            created: '2025-01-09T14:03:58.94',
            modified: '2025-01-09T14:04:13.523',
          },
          {
            id: 108853,
            title: 'Journey Lifecycle Management',
            slug: 'journey-lifecycle-management',
            category: 'examples',
            content: '<h1 id="journey-lifecycle-management">Journey Lifecycle Management</h1>\n<p>Journeys move through several&nbsp;lifecycle stages, starting in <strong>Draft</strong> and progressing through <strong>Active</strong>, <strong>Paused</strong>, and optionally <strong>Archived</strong> or <strong>Stopped</strong>. Each status affects how the journey behaves and what actions admins can take at each stage. Understanding these states helps manage enrollment, content updates, and timing for communications.</p> <h3><img alt="Journey Manager" src="https://my.axerosolutions.com/attachment?file=apLUOOB9zGoAsXDwnnljmw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="567" width="1440"></h3> <h3>Draft</h3> <p>Every journey begins in the Draft state. It can be built and reviewed but is not yet available to participants. No steps are triggered, and no users can be enrolled.</p> <p>While in Draft, admins can:</p> <ul> <li><strong>View Journey</strong> in read-only mode.</li> <li><strong>Edit Journey</strong> to define or revise its steps, messages, and settings.<br><br><em>Note:</em> When selecting content located in a private space while editing a draft that is not yet active, or when editing a standalone draft not related to any currently active journey, a popup will appear prompting to add an "Add to Space" step if one is not already included. This ensures participants will have access to the private content once the journey is activated. Only the content inside a step can be modified - for example, replacing an article with a&nbsp;wiki. The steps themselves cannot be added, removed, or reordered.</li> <li><strong>Activate Journey</strong> to make it available to users.</li> <li><strong>Delete Journey</strong> permanently (cannot be undone).</li> <li><strong>Reassign Author</strong> to another admin if needed.</li> </ul> <h3>Active</h3> <p>Once activated, a journey becomes live. Users can be enrolled and will start progressing through the defined steps. Messages and automations are delivered as configured.</p> <p>While Active, admins can:</p> <ul> <li><strong>View Journey</strong> in read-only mode.</li> <li><strong>Edit Journey</strong> content and steps. <em>Note:</em> Only the content within a step can be changed, for example, replacing an article with a wiki. Steps themselves cannot be added, removed, or reordered once the journey is active. If the step includes private content, it can only be replaced with content from the same space, as the <a href="https://my.axerosolutions.com/spaces/133/experience-team-content/wiki/view/108741/create-journey#add-space"><strong>Add to Space</strong></a> step that grants users access to the specific content cannot be changed once the journey is active. If administrators need to reconfigure Add to Space step, they must&nbsp;<strong>stop</strong> the journey first.</li> <li><strong>Manage Users</strong>, including enrolling or removing participants.</li> <li><strong>Pause Journey</strong> to temporarily halt progress.</li> <li><strong>Stop Journey</strong> to unenroll all users and return to Draft.</li> <li><strong>Archive Journey</strong> to prevent new enrollments but keep existing progress.</li> <li><strong>Reassign Author</strong> if ownership needs to change.</li> </ul> <h3>Paused</h3> <p>Pausing a journey suspends all activity. Users stay enrolled but cannot advance, and no communications are sent.</p> <p>While Paused, admins can:</p> <ul> <li><strong>View Journey</strong> in read-only mode.</li> <li><strong>Resume Journey</strong> by setting it back to Active.</li> <li><strong>Stop Journey</strong> to return it to Draft and remove all users.</li> <li><strong>Archive Journey</strong> to preserve participant state without resuming.</li> <li><strong>Reassign Author</strong> if necessary.</li> </ul> <h3>Archived</h3> <p>When a journey is archived, it no longer accepts new participants. Existing users remain at their current step, and no further messages or automations are sent.</p> <p>While Archived, admins can:</p> <ul> <li><strong>View Journey</strong> in read-only mode.</li> <li><strong>Copy Journey</strong> to start a new draft from the same structure.</li> <li><strong>Reassign Author</strong> for visibility or ownership updates.</li> </ul> <h3>Stopped</h3> <p>Stopping a journey removes all participants and returns the journey to Draft. It is used when the journey needs to be reset or discontinued.</p> <p>After stopping, the journey reverts to the <strong>Draft</strong> state, with all corresponding actions available:</p> <ul> <li><strong>View</strong>, <strong>Edit</strong>, <strong>Activate</strong>, <strong>Delete</strong>, and <strong>Reassign Author</strong>.<br>Participant progress is not retained.</li> </ul>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108853/journey-lifecycle-management',
            author: 'Vladana Garic',
            created: '2025-06-30T11:55:03.933',
            modified: '2025-06-30T11:55:14.857',
          },
          {
            id: 108851,
            title: 'Journeys',
            slug: 'journeys',
            category: 'examples',
            content: '<h1 id="journeys">Journeys</h1>\n<p>Journeys are structured and sequenced&nbsp;workflows that guide individuals through key stages of the employee lifecycle, including&nbsp;onboarding, role transitions, and skill development. They consist of steps that people must complete, like reading content, visiting links, completing forms, and meeting training requirements. These are combined with timed emails or notifications that are sent to both the employee and their supervisor.</p> <p>As employees progress through the requirements, they can also be added or removed from spaces and earn digital badges. In this way, administrators and people managers can guide individuals through transitions with information and pointers at their own pace, boosting site engagement and keeping participants on track without requiring manual follow-up.</p> <p>Unlike learning management systems (LMS), journeys do not include testing, certification, or grading. Instead, they provide a flexible way to deliver content and guide employees through structured experiences such as onboarding, performance reviews, leadership development, and compliance training (with the option to link out to LMS courses as one of the required steps).</p> <h3>Enable Journeys and Configure Menu Icons</h3> <p>To make Journeys accessible to users, enable Journeys in system settings and add the icon to the header and user account menu.&nbsp;</p> <h4>Enable Journeys in System Properties</h4> <ol> <li>Go to <strong>Control Panel &gt; System &gt; System Properties</strong>.</li> <li>Set the property <strong>JourneysEnabled</strong> to <strong>true</strong>.</li> </ol> <p><img alt="JourneysEnabled" src="https://my.axerosolutions.com/attachment?file=nYGiA%2FLpKuonQpZqGmj%2FHA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="266" width="1440"></p> <h4>Add the Journeys Icon to the Navigation Header</h4> <ol> <li>Go to <strong>Control Panel &gt; System &gt; Page Builder &gt; Header Tab</strong><span> &gt; </span><strong>Edit</strong>.<br><br> <p><img alt="Edit Header" src="https://my.axerosolutions.com/attachment?file=DXGeTeCHi3xXffNtBMNbKw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="811" width="1440"></p> </li> <li>Drag the <strong>Button</strong> widget and drop it under the <strong>Navigation</strong> section of the page layout. <br><br> <p><img alt="Add button" src="https://my.axerosolutions.com/attachment?file=WPXZdxoTp69KDUxsGvAKlw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="732" width="1440"></p> </li> <li>Configure the <strong>Journey Button</strong>. <br><br> <p><img alt="Navigation &gt; Journey button" src="https://my.axerosolutions.com/attachment?file=YgKZU%2FCir4mrw%2BxOa8BZ%2FA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="818" width="1440"></p> </li> <li><strong>Save</strong> the widget and <strong>publish</strong> the changes.</li> </ol> <p>The Journey icon will appear in the navigation header.&nbsp;</p> <p><img alt="Journeys icon in navigation menu" src="https://my.axerosolutions.com/attachment?file=drz7Pw1FKqWBL1JzVYjM5g%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="682" width="1440"></p> <h4>Add the Journey Icon to My Account</h4> <ol> <li>Go to <strong>Control Panel &gt; System &gt; Page Builder &gt; Header Tab</strong><span> &gt; </span><strong>Edit</strong>.</li> <li>Expand the <strong>Navigation</strong> widget, then open <strong>My Account</strong> and select <strong>Add Item</strong>. <br><br> <p><img alt="Add item" src="https://my.axerosolutions.com/attachment?file=oWDsJjiaI0x%2BnzIT%2BKrjZg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="818" width="1440"></p> </li> <li>Configure the Journey item.<br><br> <p><img alt="Edit Item " src="https://my.axerosolutions.com/attachment?file=3WLJ92XkcTiIHMmuU%2FqgVg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1058" width="1440"></p> </li> <li><strong>Save</strong> the widget configuration and <strong>publish</strong> the changes.</li> </ol> <p>When you click your profile or account menu, the Journey icon will be visible under <strong>My&nbsp;Apps and Tools</strong>.<img alt="My Account &gt; Apps and Tools &gt; Journey" src="https://my.axerosolutions.com/attachment?file=3DzfgWmgR44uXa7cUeUlAA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="781" width="1440"></p> <h3>Managing Journey Permissions</h3> <p>To manage who can create, edit, or delete journeys, go to <strong>Control Panel &gt; People &gt; User Section Permissions</strong>.</p> <p><strong>Note:</strong> Only users with the <strong>Manage Permissions</strong> setting enabled can modify journey access rights for other roles.<br><br><img alt="User Section Permissions" src="https://my.axerosolutions.com/attachment?file=I2TiJb5tIw9Kwtjt0EUvpA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="678" width="1440"></p> <h3>Accessing the Journeys Manager</h3> <p>Users with permission <span style="color: rgba(0, 0, 0, 1)">to create or manage journeys</span> can open the Journey Manager from their profile menu.</p> <p>To access the Journey Manager, click your <strong>profile image</strong> in the top navigation bar and select <strong>Journey</strong> under <strong>My Apps and Tools</strong>.</p> <p><img alt="Apps and Tools &gt; Journeys" src="https://my.axerosolutions.com/attachment?file=2ERH2OUQCewLg%2FTOxkbRCw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="927" width="1440"></p> <h3>Managing Journey Permissions</h3> <p>To manage who can create, edit, or delete journeys, go to <strong>Control Panel &gt; People &gt; User Section Permissions</strong>.</p> <p><strong>Note:</strong> Only users with the <strong>Manage Permissions</strong> setting enabled can modify journey access rights for other roles.<br><br><img alt="User Section Permissions" src="https://my.axerosolutions.com/attachment?file=rkXg6dzQ3ym8Ds18zsDxlA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="678" width="1440"></p> <h3>Accessing the Journeys Manager</h3> <p>Users with permission <span style="color: rgba(0, 0, 0, 1)">to create or manage journeys</span> can open the Journey Manager from their profile menu.</p> <p>To access the Journey Manager, click your <strong>profile image</strong> in the top navigation bar and select <strong>Journey</strong> under <strong>My Apps and Tools</strong>.</p> <p><img alt="Apps and Tools &gt; Journeys" src="https://my.axerosolutions.com/attachment?file=%2BOLNsfH9Kg9bpzVewAdzNg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="927" width="1440"></p> <h2 id="journeys-interface">Journeys Manager Interface</h2> <p>The Journeys Manager provides an overview of all existing journeys. Admins can search, sort, filter journeys, and view details such as status, <span style="color: rgba(0, 0, 0, 1)">creator</span>, and participant count.&nbsp;</p> <h3><img alt="Journey Manager" src="https://my.axerosolutions.com/attachment?file=Tlmf%2BH2wTiI7Q9bvxvOZZw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="566" width="1440"></h3> <h3 id="filtering-journeys">Filtering Journeys</h3> <p>The filters above the table allow you to narrow the list of journeys. You can filter by status, date range, creator, or search by name.</p> <ul> <li> <p><strong>Status:</strong> Use the dropdown menu to filter journeys by their current state. Available options include Draft, Active, Paused, and Archived.</p> </li> <li> <p><strong>Date Range:</strong> Select a <strong>Start Date</strong> and <strong>End Date</strong> to view journeys created within a specific time period.</p> </li> <li> <p><strong>Created by:</strong> Enter a user’s name in the <strong>Created by</strong> field to view journeys initiated by a particular person.</p> </li> <li> <p><strong>Search:</strong> Use the search bar on the right to search by journey name.</p> </li> </ul> <h3 id="table-columns">Table Columns</h3> <p>The table below the filters displays key details about each journey. These columns provide insight into ownership, status, and participation.</p> <ul> <li> <p><strong>Date Created:</strong> Shows when the journey was initially created.</p> </li> <li> <p><strong>Created By:</strong> Displays the name of the user who created the journey.</p> </li> <li> <p><strong>Status:</strong> Indicates whether the journey is in Draft, Active, Paused, or Archived state.</p> </li> <li> <p><strong>Enrolled:</strong>&nbsp; Shows the number of users currently enrolled in the journey.</p> </li> <li> <p><strong>Graduated:</strong> Number of users who have completed all steps.</p> </li> </ul> <h2 id="create-journey"></h2> <h3>Learn More</h3> <p><a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108852/create-journey"><strong>Create Journey</strong></a><br>Create and design a journey from scratch, add steps, configure communication blocks, and organize the sequence of actions users follow.</p> <p><a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108853/journey-lifecycle-management"><strong>Journey Lifecycle Management</strong></a><br>Edit live journeys, pause or archive them, and review how the system behaves during each lifecycle change.</p> <p><a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108854/managing-journey-participants"><strong>Managing Journey Participants</strong></a><br>Add participants manually or automatically based on set criteria. Manage enrollment settings and remove users if needed.</p> <p><a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108855/navigating-a-journey-as-a-user"><strong>Navigating a Journey as a User</strong></a><br>View what enrolled users see as they move through a journey, including step progress, delay messages, and status notifications.</p>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108851/journeys',
            author: 'Vladana Garic',
            created: '2025-06-30T11:50:57.417',
            modified: '2025-07-10T07:44:27.597',
          },
          {
            id: 108854,
            title: 'Managing Journey Participants',
            slug: 'managing-journey-participants',
            category: 'examples',
            content: '<h1 id="managing-journey-participants">Managing Journey Participants</h1>\n<p>Admins can manually control who participates in a journey. Once a journey is activated, users can be added or removed at any time through the <strong>Manage Users</strong> tab.</p> <h2 id="enrolling-users">Enroll Users in a Journey</h2> <p>Journeys must be activated before users can be added. Once active, a <strong>Manage Users</strong> tab appears in the journey editor.&nbsp; Enrolled users will immediately begin the journey and progress through the steps as defined in the builder.&nbsp;</p> <p><strong>To enroll users:</strong></p> <ol> <li>From the Journey Manager, click the three-dot menu (⋮) next to a drafted journey and select <strong>Activate Journey</strong>.<br><br><img alt="Click Activate Journey" src="https://my.axerosolutions.com/attachment?file=q5ZpndoHU%2B47FLfr8A8PHA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="604" width="1440"></li> <li>A prompt will appear confirming that users will be enrolled and messages sent according to the journey setup. Click&nbsp;<strong>Yes</strong> to proceed.<br><br><img alt="Click Yes" src="https://my.axerosolutions.com/attachment?file=zdwhWL8hmcV6%2BsPuZR7CiQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="706" width="1440"></li> <li> <p>After activation, click the three-dot menu (⋮) and select <strong>Manage Users</strong> to open the user management view.<br><br><img alt="Manage Users" src="https://my.axerosolutions.com/attachment?file=lKXNXS1TEjmXk6JpXn%2FYbQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="380" width="1440"></p> </li> <li> <p>Click <strong>Add Users</strong> to open the user selection window.<br><br><img alt="Click Add Users" src="https://my.axerosolutions.com/attachment?file=vOm8c7wrVNsYV0%2BQWG0dXA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="934" width="1430"></p> </li> <li> <p>Use the&nbsp;<strong>+ Add</strong> button to add individuals, or select multiple users and click <strong>Add Selected</strong>. You can also use the search bar to find specific users.</p> </li> <li> <p>Click <strong>Save Journey</strong> to confirm enrollment.</p> </li> </ol> <h2>Removing Participants</h2> <p>Admins can remove users from a journey at any time. When removed, users lose access to all journey steps. If re-enrolled later, they will start from the beginning.</p> <p>To remove a participant:</p> <ol> <li> <p>Go to the <strong>Manage Users</strong> tab in the active journey.</p> </li> <li> <p>Find the user you want to remove in the list.<br><br><img alt="Manage Users" src="https://my.axerosolutions.com/attachment?file=GKIAG%2FNf7hYDRiHYrdITtw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="598" width="1440"></p> </li> <li> <p>Click the red trash can icon next to their progress bar.</p> </li> <li> <p>Confirm the removal in the prompt.</p> </li> <li> <p>Click <strong>Save Journey</strong> to apply changes.</p> </li> </ol> <p>The user will be removed from the journey and will no longer appear in the participant list.</p> <h3>Tracking User Progress</h3> <p>To see how each user is progressing through the journey:</p> <ol> <li> <p>Go to <strong>Manage Users</strong>.<br><br><img alt="Manage Users" src="https://my.axerosolutions.com/attachment?file=mKgx2GPxQcQQ0crCxt641A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="567" width="1440"><em><br></em></p> </li> <li> <p>Click on a user’s progress bar or name to open their detailed view.<em><br></em></p> </li> </ol> <p><img alt="Participant progress" src="https://my.axerosolutions.com/attachment?file=Qj%2BigZGp5xH%2BXP7bLzL%2FcA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="796" width="1434"></p> <p>This displays their start date, current step, time in step, and overall progress.</p> <p><strong>Note</strong>:</p> <ul> <li> <p>If a journey is <strong>paused</strong>, no new users can be enrolled. Existing participants remain in place and will resume progress once the journey is active again.&nbsp;</p> </li> <li> <p>If a journey is <strong>stopped</strong>, all participants are removed, and the journey returns to<strong> Draft</strong> mode.</p> </li> <li> <p>If a user is <strong>removed</strong> and re-enrolled later, they will start from the beginning of the journey.</p> </li> </ul> <h2 id="user-experience"></h2>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108854/managing-journey-participants',
            author: 'Vladana Garic',
            created: '2025-06-30T11:57:35.377',
            modified: '2025-06-30T12:13:41.677',
          },
          {
            id: 108855,
            title: 'Navigating a Journey as a User',
            slug: 'navigating-a-journey-as-a-user',
            category: 'examples',
            content: '<h1 id="navigating-a-journey-as-a-user">Navigating a Journey as a User</h1>\n<p>Once a user is enrolled in a Journey, they gain access to a personalized interface that tracks their progress and guides them through each step.</p> <h3>Accessing your Journey</h3> <p>After enrollment, users receive a notification by email and/or in-app. The message includes the journey name, a brief description, and a link to open the&nbsp;<strong>My Journeys</strong> panel. From there, users can see all active journeys they are participating in, view their current step, and resume progress where they last left off.</p> <p><img alt="Notifications" src="https://my.axerosolutions.com/attachment?file=plrZVC9eBazdEcZz1iiD1w%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="625" width="1440"></p> <p>Users can also access their journeys at any time from the top navigation bar. Click the&nbsp;<strong>journey icon</strong> to open the <strong>My Journeys</strong> panel.</p> <p><img alt="Click Journey icon" src="https://my.axerosolutions.com/attachment?file=k4f5SmaXSCdSFAOiXvKAeg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="657" width="1440"></p> <p>This opens a sidebar with all active journeys, showing their progress, current step, and a button to continue where they left off.<br><br><img alt="View Journeys" src="https://my.axerosolutions.com/attachment?file=ETEUx%2FeITp6Qhgo7pG3fFw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="642" width="1440"></p> <h3>Tracking Progress</h3> <p>When a user opens a journey, a progress bar appears at the top showing the percentage of steps completed. Each step is displayed vertically in the order it was defined. Completed steps are clearly marked and can be revisited, while the current step is visually highlighted. Future steps remain locked if they depend on a previous step or are set to appear after a delay.</p> <p>If a step has a required delay (e.g., a 3-day wait), the next step remains locked and displays a message with the remaining time. Once the delay expires, users receive a notification that the next step is available.</p> <p><img alt="Viewing progress" src="https://my.axerosolutions.com/attachment?file=2oCoXgOTjTZAOPYCRfyC8w%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="689" width="1440"></p> <h3>Completing Steps</h3> <p>Each step includes a&nbsp;<strong>Mark as Complete</strong>&nbsp;button that becomes visible once the required action has been completed. This could involve reading content, watching a video, or another predefined activity. When the user marks the step as complete, the interface updates to reflect the change. The progress bar advances, and the next step unlocks, provided no delay or dependency is still in effect.</p> <p>Steps can be completed in any order unless they are part of a segment that requires sequential completion or if a preceding step is marked as "Require step completion before progression."</p> <p><img alt="Mark as completed" src="https://my.axerosolutions.com/attachment?file=ozlTU5edKdsmLE74Gdx9rg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="788" width="1440"></p> <h3>Status Changes and Notifications</h3> <p>If a journey is paused, users see it labeled as <strong>Paused</strong> in the My Journeys panel and receive a notification. They cannot proceed until it is resumed, but can still view completed steps.</p> <p>If a journey is stopped or archived, users are notified and the journey is removed from their active list. In both cases, access is revoked unless the user is re-enrolled in the future.</p>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108855/navigating-a-journey-as-a-user',
            author: 'Vladana Garic',
            created: '2025-06-30T12:00:26.987',
            modified: '2025-06-30T12:00:37.083',
          },
          {
            id: 108078,
            title: 'Webhooks',
            slug: 'webhooks',
            category: 'examples',
            content: '<h1 id="webhooks">Webhooks</h1>\n<div class="mce-toc documentation-callout"> <h2><strong>Table of Contents</strong></h2> <ul> <li><a href="#webhooks-overview">Webhooks Overview</a></li> <li><a href="#managing-webhooks">Managing Webhooks</a> <ul> <li><a href="#creating-webhook">Creating and Editing Webhook</a></li> <li><a href="#testing-webhook">Enabling and Testing Webhook</a></li> <li><a href="#monitoring-webhook-activity">Monitoring Webhook Activity</a></li> <li><a href="#filtering-webhooks">Filtering Webhooks</a></li> <li><a href="#webhook-logs">Webhook Logs</a></li> <li><a href="#deleting-webhook">Deleting Webhook</a></li> </ul> </li> </ul> </div> <h2 id="webhooks-overview">Webhooks Overview</h2> <p>Webhooks provide a real-time communication mechanism between your platform and external applications. Instead of relying on periodic API polling, webhooks allow automated HTTP requests to be sent to a specified URL when specific events occur. This significantly improves efficiency, reduces API overhead, and enables seamless integration with third-party services.</p> <p>In Axero, you can configure webhooks to automate actions based on content events, triggered when content is published, updated, expired, or deleted.&nbsp;</p> <h2 id="managing-webhooks">Managing Webhooks</h2> <div class="documentation-callout"> <p><strong>Note</strong>: To enable Webhooks functionality, set the <em>WebhookEnabled </em>system property to true.</p> </div> <p>To manage webhooks, navigate to <strong>Control Panel</strong> &gt; <strong>System</strong> &gt; <strong>Integrations</strong> &gt; <strong>Webhooks.</strong></p> <p><strong><img alt="Control Panel &gt; System &gt; Integrations &gt; Webhooks" src="https://my.axerosolutions.com/attachment?file=gHeVkkV0YbUPvUcHiCngwA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="523" width="1440"></strong></p> <p><img alt="Webhook page" src="https://my.axerosolutions.com/attachment?file=V%2BBql7yeVr%2B8iWB1hZtAyA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="544" width="1440"></p> <h3 id="creating-webhook">Creating and Editing Webhook</h3> <p>To create a new webhook:</p> <ol> <li>Navigate to <strong>Control Panel &gt; System &gt; Integrations &gt; Webhooks</strong>.<br><br> <p><img alt="Add Webhook" src="https://my.axerosolutions.com/attachment?file=QsKRKLO5zdyoOPd04bTZzQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="443" width="1440"></p> </li> <li>Click <strong>+ Add Webhook</strong> button to initiate configuration.</li> <li><strong>Add Basic Webhook Information:</strong> <ul> <li><strong>Webhook Name:</strong> Enter a name to easily identify the webhook.</li> <li><strong>Description:</strong> Provide a brief description of the webhook’s purpose (optional).<br><br> <p><img alt="Webhook Details" src="https://my.axerosolutions.com/attachment?file=nVacB%2BDmgE9eVgw%2FcjNj0g%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1102" width="1312"></p> </li> </ul> </li> <li><strong>Set Integration Settings</strong> <ul> <li><strong>Target URL:</strong> Specify the external endpoint that will receive webhook data. Ensure the URL starts with <code>https://</code>.</li> <li><strong>Headers:</strong> Add an API key for authentication.</li> <li><strong>HTTP Method:</strong> Webhooks send data using a <strong>POST</strong> request.</li> </ul> </li> <li><strong>Set Event Configurations</strong> <ul> <li><strong>Space:</strong> Select whether the webhook applies to all spaces or a specific one.</li> <li><strong>Event Type:</strong> Select Content.</li> <li><strong>Trigger On:</strong> Defines when the webhook should activate: <ul> <li><strong>Content Published:</strong> Triggered when content is published or scheduled.</li> <li><strong>Content Updated:</strong> Triggered on any edit (excluding engagement updates).</li> <li><strong>Content Expired:</strong> Triggered when content reaches expiration.</li> <li><strong>Content Deleted:</strong> Triggered when content is permanently removed.</li> </ul> <p><img alt="Webhook &gt; Event configuration" src="https://my.axerosolutions.com/attachment?file=%2FHclrBaLutDFryD5GVAZKg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1384" width="1300"></p> </li> <li><strong>Content Types:</strong> Choose one or more applicable content types, or <em>Select All</em> to include all content types.</li> <li><strong>Include Fields:</strong> Select which content fields should be included in the payload, or <em>Select All</em> to include all fields. <p><img alt="Webhooks &gt; Include Fields" src="https://my.axerosolutions.com/attachment?file=MEgsWhjscKv6ZM%2FvpZCWbA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1306" width="1298"></p> </li> </ul> </li> <li><strong>Webhook Payload Preview</strong> <p>Before finalizing a webhook, administrators can preview an example of the data payload sent when an event occurs.</p> <p><img alt="Webhook Payload Preview" src="https://my.axerosolutions.com/attachment?file=4EGxILSfiCbdKkAy8vqpEw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1596" width="1282"></p> </li> <li><strong>Saving the Webhook</strong> <p>Once you have completed the configuration steps, click <strong>Save as Draft</strong>. The webhook will be added to the list in a disabled state. You can enable it and test it later from the configuration page.</p> <p><img alt="Webhook &gt; Save as Draft" src="https://my.axerosolutions.com/attachment?file=szRkGC0Qw%2FlB9Pn8ptlfPA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="893" width="1440"></p> </li> </ol> <h4>Volume Management&nbsp;</h4> <p>You can track how often the selected&nbsp;webhook has been activated over the last&nbsp;<strong>7 days</strong>&nbsp;and&nbsp;<strong>24 hours</strong>. To view detailed trigger volume details, click the&nbsp;<strong>Edit</strong>&nbsp;button and navigate to&nbsp;<strong>Volume Management</strong>.</p> <p>The Volume Management panel appears when first creating a webhook so you can anticipate the expected volume and frequency of triggers, helping prevent overload on the site or the external endpoint. It is based on historical data for the selected webhook settings and updates dynamically if you change Spaces, Triggers, or Content Types.</p> <p><img alt="Edit Webhook &gt; Volume Management" src="https://my.axerosolutions.com/attachment?file=QyzKkoQ1LnoSikX93PVFJg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="1538" width="1326"></p> <h4>Editing Webhook</h4> <p>To edit an existing webhook, navigate to the <strong>Webhooks</strong> page and click <strong>Edit</strong> in the <strong>Actions</strong> column next to the webhook you want to modify.</p> <p>The configuration screen will open with the same fields and options used when creating a&nbsp;webhook.</p> <p>You can change the name, URL, headers, event triggers, or any other settings as needed.</p> <h3 id="testing-webhook">Enabling and Testing&nbsp;Webhook</h3> <p>A newly created webhook is disabled by default.&nbsp;You can test it in this state by clicking the <strong>Test</strong> button in the <strong>Actions</strong> column. This sends a sample request to the target URL, simulating an actual event, and is followed by a confirmation message. Test actions are not recorded in the&nbsp;webhook logs.&nbsp;</p> <p><img alt="Test Webhook" src="https://my.axerosolutions.com/attachment?file=6XGvDFRXB80iwDyJVs3eEA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="487" width="1440"></p> <p>After testing, toggle the webhook to <strong>enabled</strong>. Once enabled, it automatically triggers when the specified event occurs.&nbsp;</p> <p><img alt="Enable Webhook" src="https://my.axerosolutions.com/attachment?file=IAAMVFXUhUVqACocsW8%2Fnw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="484" width="1440"></p> <p><span style="font-size: 12pt; font-family: Arial, sans-serif; color: rgba(0, 0, 0, 1)">Webhook logs are automatically archived based on global retention policies configured in <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/20987/log-archive-settings">Log Archive Settings</a></strong>, ensuring past events remain accessible as needed.</span></p> <h3 id="monitoring-webhook-activity">Monitoring Webhook Activity</h3> <p>The <strong>Last Triggered</strong> column provides insight into webhook activity. Each entry includes:</p> <ul> <li>A&nbsp;timestamp indicating when the webhook last fired.</li> <li>The status of the last trigger (Success, Failed, or Pending).</li> <li>The event that triggered the webhook (e.g., content.published, content.updated).&nbsp;</li> </ul> <p><img alt="Webhook &gt; Test Results" src="https://my.axerosolutions.com/attachment?file=ruWW9Y342GhHag7xwmWdWg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="562" width="1440"></p> <h3 id="filtering-webhooks">Filtering Webhooks</h3> <p><img alt="Filtering Webhooks" src="https://my.axerosolutions.com/attachment?file=%2F%2FYwrTGv4tifyUyrIChbxg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="499" width="1440"></p> <p>The&nbsp;Webhook Configurations page displays a list of existing webhooks along with filtering options to narrow them down by:</p> <ul> <li> <p><strong>Event Type: </strong>Specifies whether the webhook is triggered by all system events or only content-related events.</p> </li> <li> <p><strong>Triggered On: </strong>Allows filtering based on when the webhook was triggered, with options such as Published, Updated, Expired, or Deleted.</p> </li> <li> <p><strong>Space: </strong>Defines whether the webhook applies to all spaces or a specific one.</p> </li> <li> <p><strong>Content Type: </strong>Narrows down webhooks based on the type of content they handle, such as articles, albums, or videos.</p> </li> </ul> <h3 id="webhook-logs">Webhook Logs</h3> <p><img alt="Webhook Logs" src="https://my.axerosolutions.com/attachment?file=LCIm3a4YRpoRhl%2FHzSQ0bQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="407" width="1440"></p> <p>The&nbsp;<strong>Logs</strong> page provides a complete history of all events where a&nbsp;webhook was triggered, whether it completed successfully or failed. This allows administrators to monitor webhook activity, investigate errors, and retry failed attempts.</p> <p>Each log entry displays the event timestamp, webhook name, event type, status (Success or Error), and the source of the trigger. You can view additional information about any entry by selecting <strong>View details</strong> in the Actions column.</p> <h4>Filtering Logs</h4> <p><img alt="Filtering Logs" src="https://my.axerosolutions.com/attachment?file=x6f5TyI5Es5vkyYWZpCiPg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="411" width="1440"></p> <p>The Logs page includes filtering options to help narrow the list of displayed entries based on selected criteria:</p> <ul> <li> <p><strong>Date Range: </strong>Specifies a start and end date to display logs from a specific time period.</p> </li> <li> <p><strong>Status: </strong>Filters results by All Statuses, Success, or Error.</p> </li> </ul> <p>After selecting your criteria, click <strong>Go</strong> to refresh the list, and update the results.</p> <h4>Log Details</h4> <p>Each log entry includes an option to view detailed information about the webhook event. You can access these details from the <strong>Actions</strong> column by selecting <strong>View details</strong>. This helps administrators understand what was sent, how the receiving system responded, and who triggered the event. <br>The original payload and the response from the destination system are included to support troubleshooting and confirm a successful&nbsp;webhook execution. If the&nbsp;webhook failed, the request can be retried.&nbsp;</p> <p><img alt="Log Details" src="https://my.axerosolutions.com/attachment?file=P%2BI8PCLMEuLWZSJu3gElcg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="864" width="1422"></p> <h3 id="deleting-webhook">Deleting Webhook</h3> <p>To delete a webhook, click the <strong>Delete</strong> button in the Actions column.</p> <p><img alt="Delete Webhook" src="https://my.axerosolutions.com/attachment?file=FRStRUwVwxc74OBVtFzDyQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="489" width="1440"></p> <p><img alt="Delete &gt; a confirmation dialog " src="https://my.axerosolutions.com/attachment?file=5JpSwi89Oea91a7n6sPd2Q%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" height="494" width="1440"></p> <p>&nbsp;A confirmation dialog will appear, requiring you to choose between:</p> <ol> <ul> <li>Keeping the Webhook Name in User Activity and Webhook Logs – The name remains visible in logs even after deletion.</li> <li>Redacting the Webhook Name in User Activity and Webhook Logs – The name is removed from logs for privacy.</li> </ul> </ol> <p>Deletion is permanent and cannot be undone. Click <strong>Delete</strong> to confirm or <strong>Cancel</strong> to return without making changes.<span style="color: rgba(224, 62, 45, 1)"></span></p>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108078/webhooks',
            author: 'Vladana Garic',
            created: '2025-04-28T07:54:13.327',
            modified: '2025-05-30T09:30:23.783',
          },
        ],
      },
      authentication: {
        title: 'Authentication',
        icon: 'fas fa-shield-alt',
        pages: [
          {
            id: 108641,
            title: 'SAML 2.0 SSO',
            slug: 'saml-20-sso',
            category: 'authentication',
            content:
              '<h1 id="saml-20-sso">SAML 2.0 SSO</h1>\n<p>This guide provides step-by-step instructions for integrating SAML 2.0 Single Sign-On (SSO) with your Axero platform using third-party identity providers such as Duo, Shibboleth, IBM Verify, CyberArk, and others. By following this guide, you will enable secure, seamless SSO for your organization using any standards-compliant SAML 2.0 provider.</p> <hr> <div class="mce-toc"> <h2>Overview</h2> <ul> <li><a href="#prerequisites">Prerequisites</a></li> <li><a href="#user-login-experience">User Login Experience</a></li> <li><a href="#provider-configuration">Provider Configuration</a></li> <li><a href="#axero-configuration">Axero Configuration</a></li> <li><a href="#testing">Testing Your Configuration</a></li> <li><a href="#user-management">User Management</a></li> <li><a href="#data-mapping">User Data Mapping</a></li> <li><a href="#advanced-options">Advanced Options</a></li> <li><a href="#troubleshooting">Troubleshooting</a></li> <li><a href="#best-practices">Best Practices</a></li> <li><a href="#support">Getting Support</a></li> </ul> </div> <hr> <h2 id="prerequisites">Prerequisites</h2> <ul> <li><strong>Axero intranet site</strong> with administrator access</li> <li><strong>SAML 2.0 identity provider</strong> (such as Duo, Shibboleth, IBM Verify, CyberArk, etc.) with administrative access</li> <li><strong>SSL certificate</strong> for your Axero domain (HTTPS is required for SAML security)</li> <li><strong>Network connectivity</strong> between Axero and your SAML provider</li> <li><strong>Understanding of your organization\'s user directory structure</strong> and attribute naming conventions</li> </ul> <hr> <h2 id="user-login-experience">User Login Experience</h2> <ul> <li><strong>Web:</strong> Users are redirected to your SAML provider for authentication. If the <strong>EnableAutoLoginViaSaml</strong> system property is set to <code>false</code>, users can choose between SAML SSO and Axero credentials on the login page.</li> <li><strong>Mobile App:</strong> Users are redirected to the SAML provider and authenticate via SSO, following the same process as the web platform.</li> <li><strong>Session Management:</strong> Session duration is controlled by Axero\'s authentication settings. When the <code>MakePermanentCookieForThirdPartyLogin</code> system property is set to <code>false</code>, users are logged out when the browser session ends.</li> </ul> <hr> <h2 id="provider-configuration">Provider Configuration</h2> <p><strong>Important:</strong> The exact steps and terminology vary significantly by provider. Consult your provider\'s specific SAML documentation for detailed instructions. The following are general configuration steps:</p> <ol> <li>Create a new SAML application/integration for Axero in your provider\'s admin console.</li> <li>Set the <strong>Assertion Consumer Service (ACS) URL</strong> to:<br><code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">https://youraxerosite.com/SAML/AssertionConsumerService.aspx</code><br><em>Replace "youraxerosite.com" with your actual Axero domain.</em></li> <li>Set the <strong>Entity ID</strong> (also called <strong>Audience URI</strong> or <strong>SP Entity ID</strong>) to your Axero site URL or a unique identifier of your choice.</li> <li>Configure the <strong>NameID format</strong>. Common options include: <ul> <li><strong>Email Address:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress</code></li> <li><strong>Persistent:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">urn:oasis:names:tc:SAML:2.0:nameid-format:persistent</code></li> <li><strong>Unspecified:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified</code></li> </ul> </li> <li>Configure attribute/claim mappings. The specific attribute names vary by provider, but commonly include: <ul> <li><strong>NameID:</strong> User\'s unique identifier (typically email or username)</li> <li><strong>Email:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress</code> or <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">email</code></li> <li><strong>First Name:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname</code> or <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">givenname</code></li> <li><strong>Last Name:</strong> <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname</code> or <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">surname</code></li> <li>Additional attributes as needed for your organization</li> </ul> </li> <li>Enable <strong>SAML Response Signing</strong> for security (strongly recommended).</li> <li>Download the IdP metadata XML file or note the SSO/Logout URLs and signing certificate for use in Axero configuration.</li> </ol> <hr> <h2 id="axero-configuration">Axero Configuration</h2> <ol> <li>In Axero, go to <strong>Control Panel &gt; System &gt; Single Sign On</strong>.</li> <li>Select <strong>SAML</strong> as the SSO type.</li> <li>Click <strong>Save</strong>.</li> <li>Enter the following information from your SAML provider: <ul> <li><strong>Partner Identity Provider URL:</strong> The Entity ID from your IdP</li> <li><strong>Single Sign On Service URL:</strong> The SSO endpoint URL from your IdP</li> <li><strong>Relying Party Trust Identifier:</strong> Your Axero site URL or the Entity ID you configured in your provider</li> <li><strong>Single Logout Service URL:</strong> The SLO endpoint URL from your IdP (optional — only if Single Logout is supported by your provider)</li> <li><strong>Partner Identity Certificate (CER):</strong> Upload the public signing certificate from your IdP</li> </ul> </li> <li>Click <strong>Update</strong> to save your settings.</li> <li>Click <strong>Data Mapping</strong> to configure user attribute mapping (see User Data Mapping section below).</li> </ol> <hr> <h2 id="testing">Testing Your Configuration</h2> <p><strong>Important:</strong> Always test your SAML configuration thoroughly before deploying to all users.</p> <ol> <li><strong>Test with a single user account:</strong> <ul> <li>Create or use an existing test user in your SAML provider</li> <li>Ensure the test user has all required attributes populated</li> <li>Attempt to log into Axero using SSO</li> </ul> </li> <li><strong>Verify the login flow:</strong> <ul> <li>Navigate to your Axero site</li> <li>You should be redirected to your SAML provider for authentication</li> <li>After successful authentication, you should be redirected back to Axero</li> <li>Verify that user data is properly mapped and displayed in Axero</li> </ul> </li> <li><strong>Test logout functionality:</strong> <ul> <li>Log out from Axero</li> <li>If Single Logout (SLO) is configured, verify you are logged out of both Axero and your SAML provider</li> </ul> </li> <li><strong>Test error scenarios:</strong> <ul> <li>Test with a disabled user account</li> <li>Test with a user missing required attributes</li> <li>Verify appropriate error messages are displayed</li> </ul> </li> </ol> <hr> <h2 id="user-management">User Management</h2> <ul> <li><strong>Automatic User Creation:</strong> By default, Axero creates a user account the first time someone logs in via SAML SSO (if <code>SAMLAutoUserCreation</code> is enabled).</li> <li><strong>Pre-Provisioning Users:</strong> You can add users in advance using: <ul> <li>Bulk Import (usernames must match SAML NameID values)</li> <li>Control Panel &gt; People &gt; Manage People &gt; Add User</li> <li>REST API for automated provisioning</li> </ul> </li> <li><strong>Administrator Accounts:</strong> Ensure admin account usernames match SAML user identifiers before enabling SSO, or update permissions after SSO is configured.</li> <li><strong>User Deactivation:</strong> Disabling a user in your SAML provider prevents new SSO logins, but the Axero account remains active. You must manually disable or delete users in Axero for complete access removal.</li> </ul> <hr> <h2 id="data-mapping">User Data Mapping</h2> <p>To import user profile data from your SAML provider to Axero, you need to map SAML attributes to Axero properties:</p> <ol> <li>Go to <strong>Control Panel &gt; System &gt; Single Sign On &gt; Data Mapping</strong>.</li> <li>Select <strong>SAML</strong> as the type.</li> <li>Add attribute mappings. The <strong>Property Name</strong> must match the exact attribute name sent by your SAML provider in the SAML assertion.</li> </ol> <table cellpadding="4" cellspacing="0"> <tbody> <tr> <th>SAML Attribute</th> <th>Axero Field</th> <th>Description</th> </tr> <tr> <td>NameID</td> <td>Username</td> <td>User\'s unique identifier (automatically mapped)</td> </tr> <tr> <td style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress</td> <td>Email</td> <td>Email address</td> </tr> <tr> <td style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname</td> <td>First Name</td> <td>User\'s first name</td> </tr> <tr> <td style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname</td> <td>Last Name</td> <td>User\'s last name</td> </tr> <tr> <td>department</td> <td>Department</td> <td>User\'s department (example custom attribute)</td> </tr> <tr> <td>title</td> <td>Job title</td> <td>User\'s job title (example custom attribute)</td> </tr> </tbody> </table> <p><strong>Important Notes:</strong></p> <ul> <li>Attribute names are case-sensitive and must match exactly as sent by your SAML provider</li> <li>Standard SAML claim URIs (the long URLs shown above) are recommended for better interoperability</li> <li>To verify which attributes your provider sends, use browser developer tools to inspect the SAML response during login, or contact your SAML provider administrator for the exact attribute names</li> <li>Custom attributes may use simple names (like "department") depending on your provider\'s configuration</li> </ul> <hr> <h2 id="advanced-options">Advanced Options</h2> <p>These options are configured in Axero\'s System Properties:</p> <ul> <li><strong>Disable SSO Auto Login:</strong> Set <code>EnableAutoLoginViaSaml</code> to <code>false</code> to allow users to choose between Axero and SSO login on the login page</li> <li><strong>Automatic User Creation:</strong> <code>SAMLAutoUserCreation</code> controls whether new users are created automatically on SSO login</li> <li><strong>Email Matching:</strong> <code>SAMLUserEmailMatch</code> enforces email address matching for SSO logins</li> <li><strong>Automatic User Updates:</strong> <code>SAMLAutoUserUpdate</code> controls whether user data is updated from SSO on each login</li> <li><strong>Session Persistence:</strong> <code>MakePermanentCookieForThirdPartyLogin</code> determines whether SSO sessions persist after browser closure</li> </ul> <hr> <h2 id="troubleshooting">Troubleshooting</h2> <p>Common issues and solutions when implementing SAML 2.0 SSO with Axero:</p> <h3>Authentication Issues</h3> <table style="vertical-align: top"> <thead> <tr> <th style="vertical-align: top; width: 25% !important">Issue</th> <th style="vertical-align: top; width: 25% !important">Possible Causes</th> <th style="vertical-align: top; width: 50% !important">Solution</th> </tr> </thead> <tbody> <tr> <td style="vertical-align: top">Login fails or users are not redirected to SAML provider</td> <td style="vertical-align: top">SAML SSO not enabled, incorrect provider URLs, misconfigured application settings</td> <td style="vertical-align: top">Verify that SAML SSO is enabled in Axero (<strong>Control Panel &gt; System &gt; Single Sign On</strong>). Check that all provider URLs (Entity ID, SSO URL, ACS URL) are entered correctly in both systems. Verify the SAML application is active and properly configured in your provider.</td> </tr> <tr> <td style="vertical-align: top">SSO loop or repeated redirects</td> <td style="vertical-align: top">Mismatched URLs between SAML provider and Axero, browser cache issues, incorrect ACS URL configuration</td> <td style="vertical-align: top">Ensure the Assertion Consumer Service URL (<code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">https://yourdomain/SAML/AssertionConsumerService.aspx</code>) matches exactly between your SAML provider and Axero configuration. Clear browser cookies and cache. Verify that the Entity ID/Relying Party Trust identifier is identical in both systems.</td> </tr> <tr> <td style="vertical-align: top">"Invalid SAML response" or assertion errors</td> <td style="vertical-align: top">Unsigned SAML response, certificate mismatch, incorrect Entity ID configuration</td> <td style="vertical-align: top">Ensure the SAML response from your provider is properly signed and the certificate in Axero matches the signing certificate from your provider. Verify that the Entity ID matches exactly between systems.</td> </tr> <tr> <td style="vertical-align: top">"There is no SAML configuration" or similar error</td> <td style="vertical-align: top">SAML not enabled, missing required configuration fields, configuration not saved properly</td> <td style="vertical-align: top">Verify that SAML is enabled in Axero and all required fields are completed. Ensure the SAML application is properly configured and active in your provider. Configuration changes may require a few minutes to take effect.</td> </tr> <tr> <td style="vertical-align: top">Browser-specific login issues</td> <td style="vertical-align: top">Browser cache, disabled cookies, browser extensions blocking redirects, popup blockers</td> <td style="vertical-align: top">Clear browser cache and cookies, or test with a different browser. Ensure cookies and third-party cookies are enabled. Disable browser extensions that may interfere with redirects. Check popup blocker settings.</td> </tr> </tbody> </table> <h3>Certificate and Security Issues</h3> <table style="vertical-align: top"> <thead> <tr> <th style="vertical-align: top; width: 25% !important">Issue</th> <th style="vertical-align: top; width: 25% !important">Possible Causes</th> <th style="vertical-align: top; width: 50% !important">Solution</th> </tr> </thead> <tbody> <tr> <td style="vertical-align: top">Certificate errors or invalid certificate</td> <td style="vertical-align: top">Expired certificates, incorrect certificate format, certificate mismatch between provider and Axero</td> <td style="vertical-align: top">Ensure the uploaded certificate is the correct public signing certificate from your SAML provider. The certificate must be in .cer, .crt, or .pem format and must not be expired. When your provider rotates certificates, update the certificate in Axero immediately. Verify the certificate matches the one used to sign SAML responses.</td> </tr> </tbody> </table> <h3>User Data and Attribute Mapping Issues</h3> <table style="vertical-align: top"> <thead> <tr> <th style="vertical-align: top; width: 25% !important">Issue</th> <th style="vertical-align: top; width: 25% !important">Possible Causes</th> <th style="vertical-align: top; width: 50% !important">Solution</th> </tr> </thead> <tbody> <tr> <td style="vertical-align: top">Attribute mapping or user data not syncing</td> <td style="vertical-align: top">Missing attribute configuration in SAML provider, incorrect attribute names, case sensitivity issues, attributes not included in assertion</td> <td style="vertical-align: top">Verify that attribute names in your SAML provider configuration match those in Axero\'s Data Mapping exactly (case-sensitive). Ensure the SAML provider is configured to include these attributes in assertions. Test with a user who has all required attributes populated. Contact your SAML provider administrator to confirm the exact attribute names being sent.</td> </tr> <tr> <td style="vertical-align: top">Email or username changes not reflected</td> <td style="vertical-align: top">User data sync disabled, timing of updates, manual intervention required for usernames</td> <td style="vertical-align: top">Email changes in your SAML provider will update in Axero on the user\'s next login if <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">SAMLAutoUserUpdate</code> is enabled. Username changes typically require manual updates in Axero as they affect the user\'s unique identifier.</td> </tr> <tr> <td style="vertical-align: top">Role or group information not syncing</td> <td style="vertical-align: top">Missing group/role claims configuration, incorrect role attribute format, groups not mapped</td> <td style="vertical-align: top">Configure your SAML provider to send group/role information using standard SAML attributes (such as <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">http://schemas.microsoft.com/ws/2008/06/identity/claims/role</code>). Verify users are assigned to appropriate groups in your provider and that group names are correctly configured.</td> </tr> </tbody> </table> <h3>User Provisioning and Access Issues</h3> <table style="vertical-align: top"> <thead> <tr> <th style="vertical-align: top; width: 25% !important">Issue</th> <th style="vertical-align: top; width: 25% !important">Possible Causes</th> <th style="vertical-align: top; width: 50% !important">Solution</th> </tr> </thead> <tbody> <tr> <td style="vertical-align: top">New users not being created in Axero</td> <td style="vertical-align: top">User account disabled in SAML provider, <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">SAMLAutoUserCreation</code> disabled, username format mismatch</td> <td style="vertical-align: top">Ensure the user account is active in your SAML provider and that <code style="word-wrap: break-word !important; word-break: break-all !important; white-space: pre-wrap !important; overflow-wrap: break-word !important">SAMLAutoUserCreation</code> is enabled in Axero System Properties. Verify the NameID format matches expectations. Consider pre-provisioning users in Axero with matching usernames.</td> </tr> <tr> <td style="vertical-align: top">Disabled users can still access Axero</td> <td style="vertical-align: top">No automatic account synchronization between SAML provider and Axero</td> <td style="vertical-align: top">Disabling a user in your SAML provider prevents new SSO logins, but existing Axero accounts remain active. Manually disable or delete user accounts in Axero for complete access removal. Consider implementing automated user lifecycle management processes.</td> </tr> </tbody> </table> <hr> <h2 id="best-practices">Best Practices</h2> <h3>Security Best Practices</h3> <ul> <li><strong>Always use HTTPS:</strong> Ensure all endpoints use secure HTTPS connections with valid SSL certificates. SAML 2.0 requires secure transport for security assertions.</li> <li><strong>Enable SAML Response Signing:</strong> Configure your SAML provider to sign SAML responses and ensure Axero validates these signatures.</li> <li><strong>Implement least privilege access:</strong> Grant only necessary permissions to service accounts and users involved in SAML authentication.</li> <li><strong>Minimize attribute exposure:</strong> Only configure attribute mappings for data actually needed in Axero. Avoid transmitting sensitive or unnecessary personal information.</li> <li><strong>Regular access reviews:</strong> Periodically audit user access in both your SAML provider and Axero. Remove accounts for users who no longer require access.</li> <li><strong>Secure certificate management:</strong> Maintain secure backups of certificates and private keys. Use proper certificate storage practices and limit access to certificate files.</li> <li><strong>Monitor authentication logs:</strong> Review SAML provider logs and Axero system logs regularly to identify failed authentication attempts or configuration issues.</li> </ul> <h3>Configuration Management</h3> <ul> <li><strong>Document your configuration:</strong> Maintain detailed documentation of all SSO-related settings, including: <ul> <li>SAML provider application configuration and settings</li> <li>Attribute mappings and claim configurations</li> <li>Axero SAML configuration parameters</li> <li>Certificate details and renewal schedules</li> <li>System property values and their purposes</li> </ul> </li> <li><strong>Version control configuration changes:</strong> Keep backups of working configurations before making changes to enable quick rollback.</li> <li><strong>Test in staging first:</strong> Always validate SSO configuration in a non-production environment before deploying to production. Test with multiple user accounts having different attribute combinations.</li> <li><strong>Plan rollback procedures:</strong> Document processes to quickly revert SAML configuration changes if issues arise.</li> </ul> <h3>Operational Best Practices</h3> <ul> <li><strong>Certificate lifecycle management:</strong> Monitor certificate expiration dates and plan renewals well in advance. Update certificates in Axero immediately when your SAML provider rotates them.</li> <li><strong>Plan for user lifecycle management:</strong> Establish processes for: <ul> <li>Onboarding new users with appropriate attributes in your SAML provider</li> <li>Updating user information when roles or attributes change</li> <li>Deactivating users when they leave the organization</li> </ul> </li> <li><strong>Maintain emergency access:</strong> Keep alternative authentication methods or emergency administrator accounts that don\'t depend on SAML authentication.</li> <li><strong>Communicate changes proactively:</strong> Notify users in advance of SSO-related changes, maintenance windows, or expected downtime.</li> </ul> <h3>Performance and Reliability</h3> <ul> <li><strong>Monitor system performance:</strong> Track SAML authentication response times and identify potential bottlenecks.</li> <li><strong>Plan for high availability:</strong> Consider your SAML provider\'s availability and your organization\'s uptime requirements.</li> <li><strong>Implement health checks:</strong> Set up monitoring to detect SAML provider issues, certificate problems, or connectivity issues quickly.</li> <li><strong>Capacity planning:</strong> Monitor resource usage during peak authentication times and plan for user base growth.</li> </ul> <h3>User Experience</h3> <ul> <li><strong>Provide user training:</strong> Educate users about the SSO experience, including login and logout processes.</li> <li><strong>Clear error messaging:</strong> Ensure users understand what to do when SAML authentication fails and provide alternative contact methods.</li> <li><strong>Browser compatibility guidance:</strong> Provide instructions for configuring different browsers for optimal SAML SSO experience.</li> <li><strong>Support multiple platforms:</strong> Document the authentication experience across different devices and platforms (web, mobile app).</li> </ul> <hr> <h2 id="support">Getting Support</h2> <p>If you encounter issues or need assistance, please <a href="https://my.axerosolutions.com/spaces/77/communifire-support/cases/add-edit-case/0">submit a private case</a> to the Axero support team. When submitting a support case, include:</p> <ul> <li>Detailed description of the issue and steps to reproduce</li> <li>Troubleshooting steps you\'ve already attempted</li> <li>Error messages and relevant log entries from both Axero and your SAML provider</li> <li>Browser and operating system information for affected users</li> <li>Screenshots of configuration settings (with sensitive information redacted)</li> <li>SAML response content (if available and with sensitive data removed)</li> </ul> <p>This information will help the support team provide faster and more accurate assistance.</p>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108641/saml-2-0-sso',
            author: 'Maxwell Drain',
            created: '2025-06-09T01:59:16.593',
            modified: '2025-06-09T02:00:20.343',
          },
          {
            id: 106884,
            title: 'Setup Guide: OneLogin SAML SSO',
            slug: 'setup-guide-onelogin-saml-sso',
            category: 'authentication',
            content: '<h1 id="setup-guide-onelogin-saml-sso">Setup Guide: OneLogin SAML SSO</h1>\n<div id="on-prem-installation"> <p>This guide provides step-by-step instructions to configure OneLogin for Single Sign-On (SSO) with Axero using SAML 2.0. Users will authenticate using their OneLogin credentials, with account management handled in Axero.</p> <h3 style="margin-top: 10px">Overview</h3> <ul style="margin-bottom: 0"> <li><a href="#preparation">Preparation</a></li> <li><a href="#step-1">Step 1: Configure OneLogin</a></li> <li><a href="#step-2">Step 2: Configure Axero</a></li> <li><a href="#step-3">Step 3: Test SSO Integration</a></li> <li><a href="#optional-custom-attributes">Optional: Add Custom Attributes</a></li> <li><a href="#troubleshooting">Troubleshooting</a></li> </ul> <h3 id="preparation">Preparation</h3> <p style="margin-bottom: 8px">Before you start, ensure you have:</p> <ul> <li>Administrator access to your OneLogin portal</li> <li>Site Administrator access to your Axero site</li> <li>Confirmed the following Axero <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22317/system-properties">System Properties</a> are configured: <ul> <li>Go to <strong>Control Panel &gt; System &gt; System Properties</strong></li> <li>EnableAutoLoginViaSaml = False</li> <li>SAMLAutoUserCreation = True</li> <li>SAMLAutoUserUpdate = True</li> <li>SAMLUserEmailMatch = False</li> </ul> </li> <li>Adjusted the following settings for your planned usernames: <ul> <li>Go to <strong>Control Panel &gt; System &gt; General Settings &gt; Advanced Settings</strong>.</li> <li>Update <strong>Minimum length for username</strong> and <strong>Maximum length for username</strong> as needed to accommodate your planned username format.</li> </ul> </li> </ul> <p style="margin-top: 12px">Adjusting these settings will help prevent potential errors during user creation and login.</p> <hr> <h3 id="step-1">Step 1: Configure OneLogin</h3> <ol> <li>Log in to your OneLogin Admin portal.</li> <li>Go to <strong>Applications &gt; Applications</strong> and click <strong>Add App</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=X1R4SKZOo0Im02Ktqo72dQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Search for and select <strong>SCIM Provisioner with SAML (SCIM v2 Core)</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=XJVebRwa%2Fy8YW3rQvtaf7A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Enter a display name for your application (for example, "Axero") and click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=XLEnoYWzFV1g%2BIAQgy81JQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>On the <strong>Configuration</strong> tab, enter the following: <ol style="list-style-type: lower-alpha"> <li><strong>SAML Audience URL</strong>: Enter your Axero site URL (for example, https://your-axero-site.com).</li> <li><strong>SAML Consumer URL</strong>: Enter your Axero site URL followed by <code>/SAML/AssertionConsumerService.aspx</code> (for example, https://your-axero-site.com/SAML/AssertionConsumerService.aspx).</li> <li><strong>SCIM Base URL</strong>: Enter your Axero site URL followed by <code>/api/scim/v2</code> (for example, https://your-axero-site.com/api/scim/v2). OneLogin requires this URL to be set even though you\'re only setting up SAML SSO now.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=WFlSSy4cMBzOwzrH6rWJfw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=G7De0doXHUnRp%2FtmYMuEQA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> </li> <li>On the <strong>Parameters</strong> tab, configure these required attributes: <ol style="list-style-type: lower-alpha"> <li><strong>First Name</strong> <ol> <li>Click <strong>+</strong> to add a new parameter.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=hIkx1stw4qhMRihSHKsulg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Set <strong>Field name</strong> to "FirstName".</li> <li>Select "Include in SAML assertion".</li> <li>Click <strong>Save</strong>.</li> <li>Select "First Name" from the <strong>Value</strong> dropdown.</li> <li>Click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=uR97Wah4cClc2OTH72CP8g%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> </li> <li><strong>Last Name</strong> <ol> <li>Click <strong>+</strong> to add a new parameter.</li> <li>Set <strong>Field name</strong> to "LastName".</li> <li>Select "Include in SAML assertion".</li> <li>Click <strong>Save</strong>.</li> <li>Select "Last Name" from the <strong>Value</strong> dropdown.</li> <li>Click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=xVytGw%2F3fJ6ZhWEB%2FNDE0g%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> </li> <li><strong>Email</strong> <ol> <li>Click <strong>+</strong> to add a new parameter.</li> <li>Set <strong>Field name</strong> to "Email".</li> <li>Select "Include in SAML assertion".</li> <li>Click <strong>Save</strong>.</li> <li>Select "Email" from the <strong>Value</strong> dropdown.</li> <li>Click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=tOl4pn33fE%2F65hfXwM0hZQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> </li> <li>Click <strong>Save</strong> to save your changes.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=vL4YO0ZbchrQAz7ztvE3LQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> <p class="note"><strong>Note:</strong> These attributes are case-sensitive and must match exactly as shown above for proper mapping in Axero.</p> </li> <li>On the <strong>SSO</strong> tab: <ol style="list-style-type: lower-alpha"> <li>Under <strong>SAML Signature Algorithm</strong>, select <strong>SHA-256</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=M3y2QeR82RApVd2EkWuwPQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Click <strong>Save</strong>.</li> <li>Return to the <strong>SSO</strong> tab.</li> <li>Record the following values: <ul> <li><strong>Issuer URL</strong></li> <li><strong>SAML 2.0 Endpoint (HTTP)</strong></li> <li><strong>SLO Endpoint (HTTP)<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=qRUbycNsX1aHI%2F6Y6OfDGw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"><br></strong></li> </ul> </li> <li>Under <strong>X.509 Certificate</strong>, click <strong>View Details</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=r9MjbWeTrebJV3BWWRDLoA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Under <strong>X.509 Certificate</strong>, select "X.509 PEM" and click <strong>Download</strong> to download the certificate.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=s%2F4Bet3qHXOdwRoxMu9vSw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Share the certificate in your support ticket, and Axero will convert it to the correct format and upload it to your Axero site.</li> </ol> </li> </ol> <h3 id="step-2">Step 2: Configure Axero</h3> <ol> <li>Log in to your Axero site as a Site Administrator.</li> <li>Navigate to <strong>Control Panel &gt; System &gt; Single Sign-On</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=0MCAeiiKKytMrlW90FV1Og%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Select SAML as the authentication method and click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=z5WL2NOAzKXTo8n0yshSSQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Enter the following information from the OneLogin SSO tab: <ul> <li><strong>Partner Identity Provider URL:</strong> Enter the Issuer URL.</li> <li><strong>Single Sign-On Service URL:</strong> Enter the SAML 2.0 Endpoint (HTTP).</li> <li><strong>Relying Party Trust Identifier:</strong> Enter your Axero site URL.</li> <li><strong>Single Logout Service URL:</strong> Enter the SLO Endpoint (HTTP).<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=ppQoN1gmRWJgCYiuaymptg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ul> </li> <li>Click <strong>Update</strong> to save your changes.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=eKK0NtKbpvnvtVloOxDaNg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Click the <strong>Data Mapping</strong> tab and select <strong>SAML</strong> from the dropdown.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=8brtGLsK0UfAGlskZlZWXg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Update the following mappings so that the property names match exactly as shown: <table class="mapping-table"><caption>SAML Attribute Mappings</caption> <thead> <tr> <th scope="col">Property Name</th> <th scope="col">Field</th> </tr> </thead> <tbody> <tr> <td>FirstName</td> <td>First name</td> </tr> <tr> <td>LastName</td> <td>Last name</td> </tr> <tr> <td>Email</td> <td>Email</td> </tr> </tbody> </table> <p class="note"><strong>Note:</strong> These mappings are case-sensitive and must match exactly as shown above for proper mapping in Axero.</p> </li> <li>Delete any other mappings and click <strong>Update</strong> to save your changes.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=XE5NgK2YzXZNSJMeS3kPWw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> </ol> <h3 id="step-3">Step 3: Test SSO Integration</h3> <ol> <li>In OneLogin: <ol style="list-style-type: lower-alpha"> <li>Assign yourself to the Axero application. <ol> <li>Go to <strong>Users &gt; Users</strong>.</li> <li>Select yourself from the list.</li> <li>Click the <strong>Applications</strong> tab.</li> <li>Click <strong>+</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=O50Sddt2c5ldSL0%2F8TO8sg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Select <strong>Axero</strong> from the dropdown and click <strong>Continue</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=e%2FbTeiITEnBLtmSY2bTq%2BQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Click <strong>Save</strong>.</li> </ol> </li> <li>Ensure your user profile has the required attributes (first name, last name, email).</li> </ol> </li> <li>Test SSO login: <ol style="list-style-type: lower-alpha"> <li>Follow the <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/101706/how-to-add-an-sso-login-button">instructions</a> for adding a "Login via SSO" button to your login page.</li> <li>Open a new incognito/private browser window.</li> <li>Navigate to your Axero site\'s login page.</li> <li>Click the <strong>Login via SSO</strong> button.</li> <li>Verify successful authentication and profile data mapping.</li> </ol> </li> </ol> <h3 id="optional-custom-attributes">Optional: Add Custom Attributes</h3> <ol> <li>In OneLogin, go to your application\'s <strong>Parameters</strong> tab.</li> <li>Click <strong>+</strong> to add a custom attribute.</li> <li>Enter the <strong>Field name</strong> from the following list: <table class="mapping-table"><caption>SAML Attribute Mappings</caption> <thead> <tr> <th scope="col">Field Name</th> <th scope="col">Value</th> </tr> </thead> <tbody> <tr> <td>ProfilePhoto</td> <td>Profile Picture</td> </tr> <tr> <td>Phone</td> <td>Phone</td> </tr> <tr> <td>ManagerEmail</td> <td>Manager Email</td> </tr> <tr> <td>Company</td> <td>Company</td> </tr> <tr> <td>Department</td> <td>Department</td> </tr> <tr> <td>Title</td> <td>Title</td> </tr> </tbody> </table> </li> <li>Select "Include in SAML assertion".</li> <li>Click <strong>Save</strong>.</li> <li>Select the corresponding value for the field name in step 3 from the dropdown.</li> <li>Repeat steps 2-6 for each custom attribute.</li> <li>Click <strong>Save</strong>.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=0%2BsSbWJA8kvdqubfDwFIkw%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>In Axero, go to <strong>Control Panel &gt; System &gt; Single Sign-On &gt; Data Mapping</strong>.</li> <li>Select <strong>SAML</strong> from the dropdown.</li> <li>Click <strong>Add</strong> to add a new mapping.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=ZgVDlzsSV90EtvaOuhBhDA%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Add a mapping for each desired attribute, ensuring the property names match precisely: <table class="mapping-table"><caption>SAML Attribute Mappings</caption> <thead> <tr> <th scope="col">Property Name</th> <th scope="col">Field</th> </tr> </thead> <tbody> <tr> <td>ProfilePhoto</td> <td>Profile photo</td> </tr> <tr> <td>Phone</td> <td>Phone</td> </tr> <tr> <td>ManagerEmail</td> <td>Reports to</td> </tr> <tr> <td>Company</td> <td>Company</td> </tr> <tr> <td>Department</td> <td>Department</td> </tr> <tr> <td>Title</td> <td>Occupation</td> </tr> </tbody> </table> </li> <li>Click <strong>Update</strong> to save your changes.<br><img alt="OneLogin Add App screen" src="https://my.axerosolutions.com/attachment?file=djWJrpbtYQfHsXIO9p7jFQ%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" width="800" data-action="zoom"></li> <li>Test the new attribute mappings by logging in via SSO.</li> </ol> <hr> <h3 id="troubleshooting">Troubleshooting</h3> <ul> <li><strong>User Deprovisioning:</strong> OneLogin SAML integration does not automatically deactivate users in Axero. Deactivate users manually in Axero or use SCIM provisioning.</li> <li><strong>Role Mapping:</strong> To map OneLogin roles/groups to Axero roles, use custom attributes and configure them in the Data Mapping section.</li> </ul> </div>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106884/setup-guide-onelogin-saml-sso',
            author: 'Maxwell Drain',
            created: '2025-01-01T20:04:15.047',
            modified: '2025-01-01T20:16:51.373',
          },
          {
            id: 108971,
            title: 'Transitioning to JWT Authentication',
            slug: 'transitioning-to-jwt-authentication',
            category: 'authentication',
            content: '<h1 id="transitioning-to-jwt-authentication">Transitioning to JWT Authentication</h1>\n<p>Beginning with <strong>Axero version 9.45</strong>, the platform is modernizing REST API authentication by transitioning from traditional API keys to <strong>JWT (JSON Web Token) Bearer Tokens</strong>. JWT is an industry-standard method for securely transmitting information between parties.</p> <p><strong>✅ New:</strong> All REST API calls will be authenticated using <strong>JWT Bearer Tokens</strong> with the standard format <code>Authorization: Bearer {token}</code>.</p> <p><strong>❌ Deprecated:</strong> Legacy REST API keys will no longer be visible or editable in the user interface. Existing API keys will continue to function until <strong>November 2025</strong>.</p> <hr> <h3><strong>Why This Change Benefits You</strong></h3> <p>This modernization brings significant security and operational advantages for your organization:</p> <ul> <li> <p><strong>Enhanced Security</strong> – JWT tokens use cryptographic signatures and configurable expiration times to reduce risks from credential theft, token replay attacks, and unauthorized access.</p> </li> <li> <p><strong>Greater Flexibility</strong> – Create unlimited tokens with custom names, expiration periods, and specific purposes. Each token can be tailored to individual applications, integrations, or user roles.</p> </li> <li> <p><strong>Improved Management</strong> – Gain complete visibility into active integrations with detailed token metadata, usage tracking, and the ability to instantly revoke access without affecting other integrations.</p> </li> <li> <p><strong>Industry Standards Compliance</strong> – JWT tokens align with modern authentication standards, ensuring compatibility with enterprise security tools and third-party integrations.</p> </li> </ul> <hr> <h3><strong>API Key vs. JWT Token Comparison</strong></h3> <div align="left"> <table><colgroup> <col style="width: 160px"> <col style="width: 217px"> <col style="width: 217px"> </colgroup> <thead> <tr> <th scope="col" style="vertical-align: top"> <p><strong>Feature</strong></p> </th> <th scope="col" style="vertical-align: top"> <p><strong>API Key</strong></p> </th> <th scope="col" style="vertical-align: top"> <p><strong>JWT Token (Bearer Token)</strong></p> </th> </tr> </thead> <tbody> <tr> <td style="vertical-align: top"> <p><strong>Visibility After Creation</strong></p> </td> <td style="vertical-align: top"> <p>Always visible in user preferences (stored in database)</p> </td> <td style="vertical-align: top"> <p>Displayed only once at creation for security; not stored by Axero after generation</p> </td> </tr> <tr> <td style="vertical-align: top"> <p><strong>Expiration Control</strong></p> </td> <td style="vertical-align: top"> <p>No expiration (permanent until manually deleted)</p> </td> <td style="vertical-align: top"> <p>Configurable expiration periods: minutes to years, or unlimited (if enabled by admin)</p> </td> </tr> <tr> <td style="vertical-align: top"> <p><strong>Revocation</strong></p> </td> <td style="vertical-align: top"> <p>Manual deletion required; immediate effect</p> </td> <td style="vertical-align: top"> <p>Instant revocation through UI; immediately invalidates all requests using that token</p> </td> </tr> <tr> <td style="vertical-align: top"> <p><strong>Security Level</strong></p> </td> <td style="vertical-align: top"> <p>Static credentials with no built-in expiration; higher risk if compromised</p> </td> <td style="vertical-align: top"> <p>Cryptographically signed with configurable expiration</p> </td> </tr> <tr> <td style="vertical-align: top"> <p><strong>Scope &amp; Permissions</strong></p> </td> <td style="vertical-align: top"> <p>Inherits all permissions from the user account that created it</p> </td> <td style="vertical-align: top"> <p>Inherits user permissions but can be customized with descriptive names for specific applications or integration purposes</p> </td> </tr> <tr> <td style="vertical-align: top"> <p><strong>Token Management &amp; Tracking</strong></p> </td> <td style="vertical-align: top"> <p>Basic visibility; no naming or categorization options</p> </td> <td style="vertical-align: top"> <p>Full lifecycle management: custom names, creation dates, expiration tracking, and usage monitoring</p> </td> </tr> </tbody> </table> </div> <h3><strong>Frequently Asked Questions</strong></h3> <p><strong>Q: Do I need to do anything if our organization doesn\'t currently use REST API keys?</strong><br><strong>A:</strong> No immediate action is required. However, if you plan to use the REST API in the future, JWT Bearer Tokens will be the only supported authentication method after November 2025.</p> <p><strong>Q: Can I test JWT tokens before fully switching over?</strong><br><strong>A:</strong> Yes. Both authentication methods work simultaneously during the transition period, allowing you to test JWT tokens in development environments and gradually migrate production integrations without service interruption.</p> <p><strong>Q: Can I view and manage my JWT tokens after creating them?</strong><br><strong>A:</strong> You can view token metadata (name, creation date, expiration date, status) and manage tokens (rename, revoke) by navigating to your <strong>Profile &gt; Activity Stream &gt; Integrations &gt; Authorizations</strong>. However, the actual token value cannot be retrieved after initial creation for security reasons.</p> <p><strong>Q: What should I do if I lose a JWT token?</strong><br><strong>A:</strong> If you lose a token, you must create a new one and update all applications that use it. The lost token should be revoked immediately to maintain security. This is why secure storage of tokens is critical.</p> <p><strong>Q: Can I use the same JWT token across multiple applications?</strong><br><strong>A:</strong> While technically possible, it\'s a security best practice to create separate tokens for each application or integration. This allows for better tracking, individual revocation, and follows the principle of least privilege.</p> <hr> <h3><strong>Next Steps</strong></h3> <p>Ready to transition to JWT authentication? Follow this recommended migration path:</p> <ol> <li><strong>Plan Your Migration:</strong> Inventory all current API key usage and create a migration timeline</li> <li><strong>Enable JWT Support:</strong> Work with your administrator to <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108972/enabling-api-access">enable JWT authentication</a></li> <li><strong>Create Test Tokens:</strong> <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108973/creating-bearer-tokens">Generate JWT tokens</a> for development and testing</li> <li><strong>Update Applications:</strong> Modify your integrations to use the new authentication format</li> <li><strong>Test Thoroughly:</strong> Verify all functionality works correctly with JWT tokens</li> <li><strong>Deploy Gradually:</strong> Migrate production systems one at a time to minimize risk</li> <li><strong>Monitor and Optimize:</strong> Track token usage and implement security best practices</li> <li><strong>Complete Migration:</strong> Revoke legacy API keys once all systems are successfully migrated</li> </ol> <p>For detailed implementation guidance, see <a href="https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108975/using-the-rest-api">Using the REST API</a>.</p>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108971/transitioning-to-jwt-authentication',
            author: 'Maxwell Drain',
            created: '2025-07-14T00:11:25.83',
            modified: '2025-07-14T14:29:56.353',
          },
        ],
      },
      configuration: {
        title: 'Configuration',
        icon: 'fas fa-cog',
        pages: [
          {
            id: 106382,
            title: 'How to Enable Images in Emails',
            slug: 'how-to-enable-images-in-emails',
            category: 'configuration',
            content: '<h1 id="how-to-enable-images-in-emails">How to Enable Images in Emails</h1>\n<p>To enable images in email notifications, follow these steps:</p> <ol> <li>Log in to your intranet site using an administrator account with system configuration permissions.</li> <li>Access the system properties by navigating to <strong>Control Panel &gt; System &gt; System Properties</strong>.<br><img src="https://my.axerosolutions.com/attachment?file=PN4QXo3aaFUwipGyQfKqkg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" style="margin: 8px 0 4px" width="800" data-action="zoom"><br><img src="https://my.axerosolutions.com/attachment?file=2AE18LRXtlR4gx53UM7d0A%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" style="margin: 8px 0 4px" width="800" data-action="zoom"></li> <li>In the search box, type <strong>ImagesInEmail</strong> to quickly locate the property.<br><img src="https://my.axerosolutions.com/attachment?file=4tpd9zlmBfAdrHGBTunUvg%3D%3D&Rest-Api-Key=bWF4d2VsbGRyYWluOmZhMGM3M2FmLTgzYWMtNGUwMC1iNmQyLWIyYmFkNjlmMTNjZg==" style="margin: 8px 0 4px" width="800" data-action="zoom"></li> <li>Click <strong>Edit</strong>, enable the property, and click <strong>Save</strong>.</li> </ol> <p><strong>Important Security Note:</strong> When this feature is enabled, the system will add encrypted information to image URLs to verify access permissions. This may not be suitable for organizations with less secure email systems or high-security environments.</p> <p>If you don\'t see the <strong>ImagesInEmail </strong>system property, <a href="https://my.axerosolutions.com/spaces/77/communifire-support/cases/add-edit-case/0">submit a support case</a> to request an upgrade to the latest version, which includes this feature.</p>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106382/how-to-enable-images-in-emails',
            author: 'Vladana Garic',
            created: '2024-11-27T20:41:14.093',
            modified: '2025-01-26T03:05:11.343',
          },
        ],
      },
      troubleshooting: {
        title: 'Troubleshooting',
        icon: 'fas fa-wrench',
        pages: [
          {
            id: 106387,
            title: 'Troubleshooting Guide for Blurry Images',
            slug: 'troubleshooting-guide-for-blurry-images',
            category: 'troubleshooting',
            content: '<h1 id="troubleshooting-guide-for-blurry-images">Troubleshooting Guide for Blurry Images</h1>\n<p>When implementing a photo carousel, it\'s crucial to ensure that the correct image URL is used to prevent blurry or improperly displayed images.</p> <h3>Understanding Image URL Variables in Page Builder Widgets</h3> <ul> <li> <p><code>{{ContentImageURL}}</code>: This variable displays the featured image for various content types, such as articles, blogs, and wikis. For photo content types, it displays a thumbnail image.</p> </li> <li> <p><code>{{ContentDetailImageURL}}</code>: This variable provides the path to the full-resolution version of a photo. This URL should be used when displaying images in a carousel template to ensure they appear sharp and clear.</p> </li> </ul> <h3>Common Issues: Blurry Photos in Carousel</h3> <p>If photos appear blurry in your carousel, it may be due to the use of <code>{{ContentImageURL}}</code> instead of <code>{{ContentDetailImageURL}}</code>. The <code>{{ContentImageURL}}</code> displays a smaller, lower-resolution image, which is unsuitable for larger carousel displays. To resolve this issue:</p> <ol> <li><strong>Check the Template</strong>: Review the carousel template to ensure it is designed to distinguish between photo content types and other types of content.</li> <li><strong>Modify the Template</strong>: If the carousel template does not differentiate between content types, modify it to: <ul> <li>Use <code>{{ContentDetailImageURL}}</code> for photo content types to display the full-resolution image.</li> <li>Use <code>{{ContentImageURL}}</code> for other content types where a featured image or thumbnail is appropriate.</li> </ul> </li> </ol> <h3>Correcting Thumbnail Image Dimensions</h3> <p>If thumbnail images appear stretched or have the wrong aspect ratio, it\'s likely due to incorrect dimension settings.</p> <p>For example, the below settings create portrait-oriented thumbnails that don\'t fit into landscape display boxes, causing distortion:</p> <ul> <li style="list-style-type: none"> <ul> <li>Thumbnail height (px): 225</li> <li>Thumbnail width (px): 300</li> </ul> </li> </ul> <p>These dimensions create portrait-oriented thumbnails, which do not fit appropriately into landscape-oriented display boxes. This mismatch leads to stretched or distorted images.&nbsp;</p> <h4>Steps to Resolve</h4> <ol> <li> <p><strong>Update Thumbnail Settings:</strong></p> <ul> <li>Navigate to the Photo Management Settings page: <strong>https://your-site-url.com/admin/photos/manage-photo-settings</strong></li> <li>Modify the thumbnail dimensions to: <ul> <li>Content width: 900</li> <li>Thumbnail height (px): 500</li> <li>Thumbnail width (px): 800</li> </ul> </li> </ul> </li> <li> <p><strong>Verify the Change</strong>:</p> <ul> <li>After updating the settings, upload new images to verify that the thumbnails render correctly.</li> </ul> </li> <li> <p><strong>Check the Thumbnails</strong>:</p> <ul> <li>Review the images in albums and photo lists to confirm that the thumbnails display with the correct aspect ratio and fit within the viewport as intended.</li> </ul> </li> </ol> <p>Following these steps should resolve the issue of distorted thumbnails, ensuring images are displayed clearly and properly across the site.</p> <p>Refer to <span class="mceNonEditable">{{mention:79243:9}}</span> to learn more about image settings.</p> <p>&nbsp;</p>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106387/troubleshooting-guide-for-blurry-images',
            author: 'Vladana Garic',
            created: '2024-11-28T15:35:51.007',
            modified: '2024-11-28T15:36:10.663',
          },
        ],
      },
      changelog: {
        title: 'Changelog',
        icon: 'fas fa-history',
        pages: [
          {
            id: 106881,
            title: '2022 Enhancements',
            slug: '2022-enhancements',
            category: 'changelog',
            content: '<h1 id="2022-enhancements">2022 Enhancements</h1>\n<h3 id="mcetoc_1gnflf823f">December 2022</h3> <ul> <li>Enhance your Microsoft Teams and Axero experience and <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/92227/embed-axero-as-an-app-in-microsoft-teams">Embed Axero as an App in Microsoft Teams</a></strong></li> <li>Send <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/92889/browser-notifications">Browser notifications</a></strong> through different browsers to keep your users up to date even when they are on actively on your site.</li> <li>Enhance your content by adding featured images for events through our <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/92908/rest-api-add-event-featured-image">REST API: Add Event Featured Image</a></strong></li> <li>Update event attendees through our new <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/92910/rest-api-add-users-to-an-event-as-attendees">Rest API: Add Users to an Event as Attendees</a></strong></li> </ul> <h3 id="mcetoc_1gnflf823g">November 2022</h3> <ul> <li>Add a user\'s profile information into Page Builder pages using our new<strong> </strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/92840/profile-field-widget"><strong>Profile Field Widget</strong>.</a></li> <li>Assign tags when adding files through <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/4879/rest-api-add-file">REST API: Add File</a></strong> with new optional parameter "tags".</li> </ul> <h3 id="mcetoc_1gnflf823h">October 2022</h3> <ul> <li>Users can now <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21520/my-bookmarks">bookmark</a> </strong>any page on their site.</li> <li>Administrators can set <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21630/password">Password expiration</a></strong>.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/2115/photos">Photos and Photo albums</a></strong> can now be sorted Alphabetically.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22190/email-settings#o365Email">Email settings</a></strong> for sending emails via an O365 account have been updated.</li> <li>Sending times for <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22683/weekly-digest-email#mcetoc_1elo3vsbu1">weekly</a> </strong>and <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22682/daily-digest-email#mcetoc_1elo492412">daily digest</a> </strong>emails can be set by administrators.</li> <li>Confirmed reading can now be used for <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22169/add-badge-challenge">badge challenge</a></strong>.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21483/create-forum">Creating a forum</a></strong> in a Space can now be done without navigating to the Space control panel.</li> <li>Parent URL now returned for content in <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/595/rest-api-get-content-list">REST API: Get Content List</a></strong>.</li> </ul> <h3 id="mcetoc_1gnflf823i">September 2022</h3> <ul> <li>Mobile layout now has <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/78459/dark-mode">Dark Mode</a> </strong>available.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22589/people-widget">People Widget</a></strong> now can be now be built with Personas and multiple users widget template.</li> <li>UI update for <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/89023/notifications-for-content">Notify People</a></strong> functionality</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21130/create-wiki-page">"Publication Date"</a></strong> is now available for Wiki Content Type</li> </ul> <h3 id="mcetoc_1gnflf823j">August 2022</h3> <ul> <li>Persona Actions now include <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/86770/personas-include-actions"><strong>assigning space roles</strong>.</a></li> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21520/my-bookmarks"><strong>Bookmarks</strong></a> can now be sorted.</li> <li>Individual events can now be edited or deleted in a <span style="text-decoration: underline"><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21478/events">recurring calendar event.</a></strong></span></li> <li>Update to <strong><span style="text-decoration: underline"><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/38400/recognition">Recognition Leaderboard</a></span> </strong>to include Challenges.</li> <li><span style="text-decoration: underline"><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/26794/outlook-sync-administrator-setup">Outlook integration</a></strong></span> updated to manage recurring calendar events.</li> <li>Update a user profile field with <span style="text-decoration: underline"><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/27247/rest-api-update-user-profile-fields">multiple values via API</a>.</strong></span></li> <li>Views of Page Builder pages are now logged in <strong><span style="text-decoration: underline"><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/23907/audience-overview">analytics</a>.</span></strong></li> </ul> <h3 id="mcetoc_1gnflf823k">July 2022</h3> <ul> <li>Unique Viewers column have been added to the <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/38462/top-content">Top Content</a></strong> CSV download.</li> <li>Users can now <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21070/required-reading">send reminder notifications for required reading content</a></strong>.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/88998/personas-in-spaces">Multiple Personas</a></strong> can be added to Spaces.</li> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/4652/convert-resources-change-the-language-of-your-intranet"><strong>Professional translation of System Resources</strong></a><strong> </strong>is now available for Italian and Portuguese</li> <li>Ability to sync <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/42136/okta-sso#OktaGroupingCONTENT">Groups from Okta to Axero</a></strong></li> </ul> <h3 id="mcetoc_1gnflf823l">June 2022</h3> <ul> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22597/search-widget">Search Widget</a></strong> is now available on mobile page builder.</li> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22551/raw-html-widget"><strong>Raw HTML Widget</strong></a> is now available on mobile mobile page builder.</li> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/21519/connect-email-to-calendar"><strong>Default Outlook email account</strong></a> option to send event details from.</li> <li>Pressing enter in search box will show user results in <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22062/manage-people">Manage People</a></strong></li> </ul> <h3 id="mcetoc_1gnflf823m">May 2022</h3> <ul> <li><span style="text-decoration: underline"><strong>{{mention:90408:9}}</strong></span> available for users.</li> </ul> <h3 id="mcetoc_1gnflf823n">April 2022</h3> <ul> <li><span style="text-decoration: underline"><strong>{{mention:89872:9}}</strong></span> now available.</li> <li><span style="text-decoration: underline"><strong>{{mention:89625:9}}</strong></span> available for users.</li> <li><span style="text-decoration: underline">{{mention:89931:9}}</span> option available.</li> </ul> <h3 id="mcetoc_1gnflf823o">March 2022</h3> <ul> <li><span style="text-decoration: underline"><strong>{{mention:89437:9}} </strong></span>are now available for users.</li> <li><strong><span style="text-decoration: underline">{{mention:21478:9}}</span> </strong>now has an option for Maximum number of attendees.</li> </ul> <h3 id="mcetoc_1gnflf823p">February 2022</h3> <ul> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/88998/personas-in-spaces">Space level personas</a></strong> are now available for creation. (Beta release)</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/89023/notifications-for-content">Custom Notifications</a></strong> are now available for content activity.</li> <li>REST API: Create and automate <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/89109/rest-api-url-mapping-api">multiple URL Redirects</a></strong></li> </ul> <h3 id="mcetoc_1gnflf823q">January 2022</h3> <ul> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/88380/promoted-search" rel="noopener" target="_blank"><strong>Promoted Search</strong></a> option now available for content.</li> <li>Profile pages are an option in <strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22188/site-settings">site settings</a> </strong>to be the landing page when users log in.</li> <li><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/88239/force-password-change" rel="noopener" target="_blank"><strong>Force Password Change</strong></a> for users.</li> <li><strong><a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/88056/splunk-integration-setup" rel="noopener" target="_blank">Splunk services</a></strong> can now integrate with Axero data.</li> <li>Create sets of words that relate to each other with <a href="https://my.axerosolutions.com/spaces/5/communifire-documentation/wiki/view/22607/search#mcetoc_dysearchsyn" rel="noopener" target="_blank"><strong>Search Synonyms</strong></a>.</li> </ul>',
            url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106881/2022-enhancements',
            author: 'Maxwell Drain',
            created: '2024-12-31T14:09:48.553',
            modified: '2024-12-31T23:11:36.46',
          },
        ],
      },
    };
  }

  getPageIndex() {
    return {
      introduction: {
        title: 'Introduction',
        category: 'introduction',
        subcategory: null,
        order: 1,
        difficulty: 'beginner',
        url: '#introduction',
        author: 'Documentation Team',
      },
      'how-to-enable-images-in-emails': {
        title: 'How to Enable Images in Emails',
        category: 'setup-configuration',
        subcategory: 'email-settings',
        order: 3,
        difficulty: 'beginner',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106382/how-to-enable-images-in-emails',
        author: 'Vladana Garic',
      },
      'troubleshooting-guide-for-blurry-images': {
        title: 'Troubleshooting Guide for Blurry Images',
        category: 'troubleshooting',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106387/troubleshooting-guide-for-blurry-images',
        author: 'Vladana Garic',
      },
      'rest-api-rebuild-index': {
        title: 'REST API: Rebuild Index',
        category: 'endpoints',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106388/rest-api-rebuild-index',
        author: 'Vladana Garic',
      },
      'rest-api-get-search-activity-analytics': {
        title: 'REST API: Get Search Activity Analytics',
        category: 'endpoints',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106624/rest-api-get-search-activity-analytics',
        author: 'Vladana Garic',
      },
      'rest-api-get-search-content-analytics': {
        title: 'REST API: Get Search Content Analytics',
        category: 'endpoints',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106625/rest-api-get-search-content-analytics',
        author: 'Vladana Garic',
      },
      '2022-enhancements': {
        title: '2022 Enhancements',
        category: 'changelog',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106881/2022-enhancements',
        author: 'Maxwell Drain',
      },
      'setup-guide-onelogin-saml-sso': {
        title: 'Setup Guide: OneLogin SAML SSO',
        category: 'authentication',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106884/setup-guide-onelogin-saml-sso',
        author: 'Maxwell Drain',
      },
      'file-field': {
        title: 'File Field',
        category: 'examples',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/106976/file-field',
        author: 'Vladana Garic',
      },
      'rest-api-update-task': {
        title: 'REST API: Update Task',
        category: 'endpoints',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107073/rest-api-update-task',
        author: 'Vladana Garic',
      },
      'rest-api-get-url-mapping': {
        title: 'REST API: Get URL Mapping',
        category: 'endpoints',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107228/rest-api-get-url-mapping',
        author: 'Vladana Garic',
      },
      'rest-api-add-url-mapping': {
        title: 'REST API: Add URL Mapping',
        category: 'endpoints',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107229/rest-api-add-url-mapping',
        author: 'Vladana Garic',
      },
      'rest-api-delete-url-mapping': {
        title: 'REST API: Delete URL Mapping',
        category: 'endpoints',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107230/rest-api-delete-url-mapping',
        author: 'Vladana Garic',
      },
      'rest-api-get-axero-copilot-conversation-records': {
        title: 'REST API: Get Axero Copilot Conversation Records',
        category: 'endpoints',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107231/rest-api-get-axero-copilot-conversation-records',
        author: 'Vladana Garic',
      },
      'create-wiki-page': {
        title: 'Create Wiki Page',
        category: 'examples',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/107911/create-wiki-page',
        author: 'Maxwell Drain',
      },
      webhooks: {
        title: 'Webhooks',
        category: 'examples',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108078/webhooks',
        author: 'Vladana Garic',
      },
      'saml-20-sso': {
        title: 'SAML 2.0 SSO',
        category: 'authentication',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108641/saml-2-0-sso',
        author: 'Maxwell Drain',
      },
      journeys: {
        title: 'Journeys',
        category: 'examples',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108851/journeys',
        author: 'Vladana Garic',
      },
      'create-journey': {
        title: 'Create Journey',
        category: 'examples',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108852/create-journey',
        author: 'Vladana Garic',
      },
      'journey-lifecycle-management': {
        title: 'Journey Lifecycle Management',
        category: 'examples',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108853/journey-lifecycle-management',
        author: 'Vladana Garic',
      },
      'managing-journey-participants': {
        title: 'Managing Journey Participants',
        category: 'examples',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108854/managing-journey-participants',
        author: 'Vladana Garic',
      },
      'navigating-a-journey-as-a-user': {
        title: 'Navigating a Journey as a User',
        category: 'examples',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108855/navigating-a-journey-as-a-user',
        author: 'Vladana Garic',
      },
      'transitioning-to-jwt-authentication': {
        title: 'Transitioning to JWT Authentication',
        category: 'authentication',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108971/transitioning-to-jwt-authentication',
        author: 'Maxwell Drain',
      },
      'enabling-api-access': {
        title: 'Enabling API Access',
        category: 'examples',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108972/enabling-api-access',
        author: 'Maxwell Drain',
      },
      'creating-bearer-tokens': {
        title: 'Creating Bearer Tokens',
        category: 'examples',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108973/creating-bearer-tokens',
        author: 'Maxwell Drain',
      },
      'using-the-rest-api': {
        title: 'Using the REST API',
        category: 'endpoints',
        url: 'https://my.axerosolutions.com/spaces/5/axero-documentation/wiki/view/108975/using-the-rest-api',
        author: 'Maxwell Drain',
      },
    };
  }
}

// Initialize the enhanced documentation site when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new EnhancedDocumentationSite();
});
